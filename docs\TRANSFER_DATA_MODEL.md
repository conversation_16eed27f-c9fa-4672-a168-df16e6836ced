# 转账功能数据模型设计

## 1. 数据库表设计

### 1.1 转账记录表 (transfers)

```sql
CREATE TABLE IF NOT EXISTS transfers (
    id TEXT PRIMARY KEY,                    -- 转账记录唯一ID
    user_id TEXT NOT NULL,                  -- 用户ID
    from_account_id TEXT NOT NULL,          -- 转出账户ID
    to_account_id TEXT NOT NULL,            -- 转入账户ID
    amount REAL NOT NULL,                   -- 转账金额
    fee REAL NOT NULL DEFAULT 0,            -- 手续费
    currency TEXT NOT NULL,                 -- 货币类型
    description TEXT,                       -- 转账描述/备注
    status TEXT NOT NULL DEFAULT 'pending', -- 转账状态: pending, completed, failed, cancelled
    transfer_type TEXT NOT NULL,            -- 转账类型: bank_to_ewallet, bank_to_bank, ewallet_to_ewallet, ewallet_to_bank
    reference_number TEXT,                  -- 转账参考号
    created_at TEXT NOT NULL,               -- 创建时间
    completed_at TEXT,                      -- 完成时间
    failed_at TEXT,                         -- 失败时间
    failure_reason TEXT,                    -- 失败原因
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (from_account_id) REFERENCES accounts (id),
    FOREIGN KEY (to_account_id) REFERENCES accounts (id)
);
```

### 1.2 转账配置表 (transfer_config)

```sql
CREATE TABLE IF NOT EXISTS transfer_config (
    id TEXT PRIMARY KEY,                    -- 配置ID
    transfer_type TEXT NOT NULL UNIQUE,     -- 转账类型
    fee_type TEXT NOT NULL,                 -- 手续费类型: fixed, percentage
    fee_amount REAL,                        -- 固定手续费金额
    fee_rate REAL,                          -- 百分比手续费率
    fee_min REAL,                           -- 最小手续费
    fee_max REAL,                           -- 最大手续费
    limit_min REAL NOT NULL,                -- 最小转账金额
    limit_max REAL NOT NULL,                -- 最大转账金额
    daily_limit REAL,                       -- 日转账限额
    monthly_limit REAL,                     -- 月转账限额
    is_active BOOLEAN DEFAULT 1,            -- 是否启用
    created_at TEXT NOT NULL,               -- 创建时间
    updated_at TEXT NOT NULL                -- 更新时间
);
```

### 1.3 转账模板表 (transfer_templates)

```sql
CREATE TABLE IF NOT EXISTS transfer_templates (
    id TEXT PRIMARY KEY,                    -- 模板ID
    user_id TEXT NOT NULL,                  -- 用户ID
    template_name TEXT NOT NULL,            -- 模板名称
    from_account_id TEXT NOT NULL,          -- 默认转出账户
    to_account_id TEXT NOT NULL,            -- 默认转入账户
    amount REAL,                            -- 默认金额（可选）
    description TEXT,                       -- 默认描述
    is_favorite BOOLEAN DEFAULT 0,          -- 是否收藏
    usage_count INTEGER DEFAULT 0,          -- 使用次数
    last_used_at TEXT,                      -- 最后使用时间
    created_at TEXT NOT NULL,               -- 创建时间
    updated_at TEXT NOT NULL,               -- 更新时间
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (from_account_id) REFERENCES accounts (id),
    FOREIGN KEY (to_account_id) REFERENCES accounts (id)
);
```

### 1.4 转账限额记录表 (transfer_limits_usage)

```sql
CREATE TABLE IF NOT EXISTS transfer_limits_usage (
    id TEXT PRIMARY KEY,                    -- 记录ID
    user_id TEXT NOT NULL,                  -- 用户ID
    transfer_type TEXT NOT NULL,            -- 转账类型
    date TEXT NOT NULL,                     -- 日期 (YYYY-MM-DD)
    daily_amount REAL DEFAULT 0,            -- 当日累计转账金额
    daily_count INTEGER DEFAULT 0,          -- 当日转账次数
    monthly_amount REAL DEFAULT 0,          -- 当月累计转账金额
    monthly_count INTEGER DEFAULT 0,        -- 当月转账次数
    created_at TEXT NOT NULL,               -- 创建时间
    updated_at TEXT NOT NULL,               -- 更新时间
    FOREIGN KEY (user_id) REFERENCES users (id),
    UNIQUE(user_id, transfer_type, date)
);
```

## 2. 数据库索引设计

```sql
-- 转账记录表索引
CREATE INDEX IF NOT EXISTS idx_transfers_user_id ON transfers(user_id);
CREATE INDEX IF NOT EXISTS idx_transfers_from_account ON transfers(from_account_id);
CREATE INDEX IF NOT EXISTS idx_transfers_to_account ON transfers(to_account_id);
CREATE INDEX IF NOT EXISTS idx_transfers_status ON transfers(status);
CREATE INDEX IF NOT EXISTS idx_transfers_created_at ON transfers(created_at);
CREATE INDEX IF NOT EXISTS idx_transfers_user_date ON transfers(user_id, created_at);

-- 转账模板表索引
CREATE INDEX IF NOT EXISTS idx_transfer_templates_user_id ON transfer_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_transfer_templates_favorite ON transfer_templates(user_id, is_favorite);
CREATE INDEX IF NOT EXISTS idx_transfer_templates_usage ON transfer_templates(user_id, usage_count DESC);

-- 转账限额使用记录表索引
CREATE INDEX IF NOT EXISTS idx_transfer_limits_user_date ON transfer_limits_usage(user_id, date);
CREATE INDEX IF NOT EXISTS idx_transfer_limits_user_type_date ON transfer_limits_usage(user_id, transfer_type, date);
```

## 3. API接口设计

### 3.1 转账相关API

#### 3.1.1 创建转账
```
POST /api/transfers/
Content-Type: application/json

Request Body:
{
    "from_account_id": "account_123",
    "to_account_id": "account_456", 
    "amount": 100.00,
    "currency": "MYR",
    "description": "转账给朋友",
    "template_id": "template_789" // 可选，使用模板时提供
}

Response:
{
    "success": true,
    "data": {
        "transfer_id": "transfer_abc123",
        "reference_number": "TXN20241208001",
        "fee": 1.00,
        "total_amount": 101.00,
        "estimated_completion": "2024-12-08T10:30:00Z"
    }
}
```

#### 3.1.2 获取转账记录
```
GET /api/transfers/
Query Parameters:
- page: 页码 (默认: 1)
- limit: 每页数量 (默认: 20)
- status: 状态筛选 (pending, completed, failed)
- from_date: 开始日期
- to_date: 结束日期
- account_id: 账户ID筛选

Response:
{
    "success": true,
    "data": {
        "transfers": [...],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 100,
            "pages": 5
        }
    }
}
```

#### 3.1.3 获取转账详情
```
GET /api/transfers/{transfer_id}

Response:
{
    "success": true,
    "data": {
        "id": "transfer_abc123",
        "from_account": {...},
        "to_account": {...},
        "amount": 100.00,
        "fee": 1.00,
        "currency": "MYR",
        "status": "completed",
        "reference_number": "TXN20241208001",
        "created_at": "2024-12-08T10:00:00Z",
        "completed_at": "2024-12-08T10:30:00Z"
    }
}
```

#### 3.1.4 验证转账信息
```
POST /api/transfers/validate
Content-Type: application/json

Request Body:
{
    "from_account_id": "account_123",
    "to_account_id": "account_456",
    "amount": 100.00
}

Response:
{
    "success": true,
    "data": {
        "is_valid": true,
        "fee": 1.00,
        "total_amount": 101.00,
        "remaining_daily_limit": 4900.00,
        "warnings": [],
        "errors": []
    }
}
```

### 3.2 转账配置API

#### 3.2.1 获取转账配置
```
GET /api/transfers/config

Response:
{
    "success": true,
    "data": {
        "transfer_types": {
            "bank_to_ewallet": {
                "fee_type": "fixed",
                "fee_amount": 1.00,
                "limit_min": 1.00,
                "limit_max": 10000.00,
                "daily_limit": 5000.00
            },
            "bank_to_bank": {
                "fee_type": "percentage",
                "fee_rate": 0.001,
                "fee_min": 1.00,
                "fee_max": 10.00,
                "limit_min": 1.00,
                "limit_max": 50000.00,
                "daily_limit": 20000.00
            }
        }
    }
}
```

### 3.3 转账模板API

#### 3.3.1 获取转账模板
```
GET /api/transfers/templates

Response:
{
    "success": true,
    "data": {
        "templates": [
            {
                "id": "template_123",
                "template_name": "给妈妈转生活费",
                "from_account": {...},
                "to_account": {...},
                "amount": 500.00,
                "is_favorite": true,
                "usage_count": 15
            }
        ]
    }
}
```

#### 3.3.2 创建转账模板
```
POST /api/transfers/templates
Content-Type: application/json

Request Body:
{
    "template_name": "给朋友转账",
    "from_account_id": "account_123",
    "to_account_id": "account_456",
    "amount": 100.00,
    "description": "朋友聚餐费用"
}

Response:
{
    "success": true,
    "data": {
        "template_id": "template_789"
    }
}
```

## 4. 数据模型关系图

```
Users (用户)
  ├── Accounts (账户)
  ├── Transfers (转账记录)
  │   ├── from_account_id → Accounts
  │   └── to_account_id → Accounts
  ├── Transfer_Templates (转账模板)
  │   ├── from_account_id → Accounts
  │   └── to_account_id → Accounts
  └── Transfer_Limits_Usage (限额使用记录)

Transfer_Config (转账配置) - 全局配置，不关联用户
```

## 5. 数据验证规则

### 5.1 转账记录验证
- 转出账户和转入账户必须属于同一用户
- 转出账户和转入账户不能相同
- 转账金额必须大于0
- 货币类型必须一致
- 转出账户余额必须足够（包含手续费）

### 5.2 转账配置验证
- 手续费率必须在0-1之间
- 最小限额不能大于最大限额
- 日限额不能小于最大单笔限额

### 5.3 转账模板验证
- 模板名称不能为空
- 同一用户的模板名称不能重复
- 关联的账户必须存在且属于当前用户
