<template>
  <div class="template-add-page">
    <van-nav-bar
      title="创建转账模板"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    />

    <div class="template-content">
      <van-form @submit="handleSubmit">
        <!-- 模板基本信息 -->
        <van-cell-group inset title="模板信息">
          <van-field
            v-model="form.templateName"
            label="模板名称"
            placeholder="请输入模板名称"
            :rules="[{ required: true, message: '请输入模板名称' }]"
            maxlength="20"
            show-word-limit
          />
        </van-cell-group>

        <!-- 转出账户选择 -->
        <van-cell-group inset title="转出账户">
          <van-field
            v-model="selectedFromAccountName"
            label="转出账户"
            placeholder="请选择转出账户"
            readonly
            is-link
            @click="showFromAccountPicker = true"
            :rules="[{ required: true, message: '请选择转出账户' }]"
          >
            <template #right-icon>
              <van-icon name="arrow" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 转入账户选择 -->
        <van-cell-group inset title="转入账户">
          <van-field
            v-model="selectedToAccountName"
            label="转入账户"
            placeholder="请选择转入账户"
            readonly
            is-link
            @click="showToAccountPicker = true"
            :rules="[{ required: true, message: '请选择转入账户' }]"
          >
            <template #right-icon>
              <van-icon name="arrow" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 默认金额和备注 -->
        <van-cell-group inset title="默认设置">
          <van-field
            v-model="form.amount"
            type="number"
            label="默认金额"
            placeholder="请输入默认金额（可选）"
          >
            <template #left-icon>
              <span class="currency-symbol">{{ getCurrencySymbol(transferCurrency) }}</span>
            </template>
          </van-field>

          <van-field
            v-model="form.description"
            label="默认备注"
            placeholder="请输入默认备注（可选）"
            maxlength="100"
            show-word-limit
          />
        </van-cell-group>

        <!-- 模板设置 -->
        <van-cell-group inset title="模板设置">
          <van-cell title="设为收藏">
            <template #right-icon>
              <van-switch v-model="form.isFavorite" />
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 预览信息 -->
        <van-cell-group v-if="isFormValid" inset title="模板预览">
          <van-cell title="模板名称" :value="form.templateName" />
          <van-cell title="转账路径" :value="transferPath" />
          <van-cell 
            title="默认金额" 
            :value="form.amount ? formatCurrency(parseFloat(form.amount), transferCurrency) : '无'"
          />
          <van-cell title="默认备注" :value="form.description || '无'" />
          <van-cell title="收藏状态" :value="form.isFavorite ? '是' : '否'" />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            block
            native-type="submit"
            :loading="loading"
            :disabled="!isFormValid"
          >
            <van-icon name="bookmark-o" />
            创建模板
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 转出账户选择器 -->
    <van-popup v-model:show="showFromAccountPicker" position="bottom" round>
      <van-picker
        :columns="fromAccountColumns"
        @confirm="onFromAccountConfirm"
        @cancel="showFromAccountPicker = false"
        title="选择转出账户"
      />
    </van-popup>

    <!-- 转入账户选择器 -->
    <van-popup v-model:show="showToAccountPicker" position="bottom" round>
      <van-picker
        :columns="toAccountColumns"
        @confirm="onToAccountConfirm"
        @cancel="showToAccountPicker = false"
        title="选择转入账户"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { transfersAPI } from '@/api/transfers'
import { showToast } from 'vant'
import { formatCurrency, formatAccountType } from '@/utils/format'
import { getCurrencySymbol } from '@/config/currencies'

const router = useRouter()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showFromAccountPicker = ref(false)
const showToAccountPicker = ref(false)

// 表单数据
const form = reactive({
  templateName: '',
  fromAccountId: '',
  toAccountId: '',
  amount: '',
  description: '',
  isFavorite: false
})

// 计算属性
const selectedFromAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === form.fromAccountId)
})

const selectedToAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === form.toAccountId)
})

const selectedFromAccountName = computed(() => {
  if (!selectedFromAccount.value) return ''
  return `${selectedFromAccount.value.name} (${formatAccountType(selectedFromAccount.value.type)})`
})

const selectedToAccountName = computed(() => {
  if (!selectedToAccount.value) return ''
  return `${selectedToAccount.value.name} (${formatAccountType(selectedToAccount.value.type)})`
})

const transferCurrency = computed(() => {
  return selectedFromAccount.value?.currency || 'MYR'
})

const transferPath = computed(() => {
  if (!selectedFromAccount.value || !selectedToAccount.value) return ''
  return `${selectedFromAccount.value.name} → ${selectedToAccount.value.name}`
})

// 转出账户选择器选项
const fromAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account => account.type !== 'investment' || account.name === 'KDI')
    .map(account => ({
      text: `${account.name} (${formatAccountType(account.type)})`,
      value: account.id
    }))
})

// 转入账户选择器选项
const toAccountColumns = computed(() => {
  if (!selectedFromAccount.value) return []
  
  return accountsStore.accounts
    .filter(account => 
      account.currency === selectedFromAccount.value.currency &&
      account.id !== form.fromAccountId &&
      account.type !== 'investment'
    )
    .map(account => ({
      text: `${account.name} (${formatAccountType(account.type)})`,
      value: account.id
    }))
})

const isFormValid = computed(() => {
  return form.templateName.trim() && 
         form.fromAccountId && 
         form.toAccountId
})

// 方法
const onFromAccountConfirm = ({ selectedOptions }) => {
  form.fromAccountId = selectedOptions[0]?.value || ''
  // 重置转入账户选择
  form.toAccountId = ''
  showFromAccountPicker.value = false
}

const onToAccountConfirm = ({ selectedOptions }) => {
  form.toAccountId = selectedOptions[0]?.value || ''
  showToAccountPicker.value = false
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    showToast('请完善模板信息')
    return
  }

  loading.value = true

  try {
    const templateData = {
      template_name: form.templateName.trim(),
      from_account_id: form.fromAccountId,
      to_account_id: form.toAccountId,
      amount: form.amount ? parseFloat(form.amount) : null,
      description: form.description.trim(),
      is_favorite: form.isFavorite
    }

    const response = await transfersAPI.createTemplate(templateData)

    if (response.success) {
      showToast({
        message: '模板创建成功',
        type: 'success'
      })

      // 返回模板列表页面
      router.back()
    } else {
      showToast({
        message: response.error || '创建失败',
        type: 'fail'
      })
    }
  } catch (error) {
    console.error('创建模板失败:', error)
    showToast({
      message: '创建失败，请重试',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 加载账户数据
  try {
    await accountsStore.fetchAccounts()
  } catch (error) {
    console.error('加载账户失败:', error)
    showToast('加载账户失败')
  }
})
</script>

<style scoped>
.template-add-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.template-content {
  padding: 16px;
}

.currency-symbol {
  font-weight: bold;
  color: #1989fa;
  margin-right: 4px;
}

.submit-section {
  margin-top: 24px;
  padding: 0 16px;
}

.submit-section .van-button {
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}
</style>
