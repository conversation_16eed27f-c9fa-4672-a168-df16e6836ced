/**
 * 转账功能前端测试用例
 * 测试转账页面的各种功能和交互
 */

import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import Transfer from '@/views/Transactions/Transfer.vue'
import { useAccountsStore } from '@/stores/accounts'
import { transfersAPI } from '@/api/transfers'

// Mock API
jest.mock('@/api/transfers', () => ({
  transfersAPI: {
    getConfig: jest.fn(),
    validateTransfer: jest.fn(),
    createTransfer: jest.fn()
  }
}))

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/transfer', component: Transfer }
  ]
})

describe('Transfer.vue', () => {
  let wrapper
  let accountsStore

  beforeEach(() => {
    setActivePinia(createPinia())
    accountsStore = useAccountsStore()
    
    // Mock accounts data
    accountsStore.accounts = [
      {
        id: 'account1',
        name: 'Test Bank',
        type: 'bank',
        currency: 'MYR',
        current_balance: 1000
      },
      {
        id: 'account2',
        name: 'Test eWallet',
        type: 'ewallet',
        currency: 'MYR',
        current_balance: 500
      },
      {
        id: 'account3',
        name: 'USD Bank',
        type: 'bank',
        currency: 'USD',
        current_balance: 800
      }
    ]

    // Mock API responses
    transfersAPI.getConfig.mockResolvedValue({
      success: true,
      data: {
        transfer_types: {
          'bank_to_ewallet': {
            fee_type: 'fixed',
            fee_amount: 1.0,
            limit_min: 1.0,
            limit_max: 10000.0,
            daily_limit: 5000.0
          }
        }
      }
    })

    transfersAPI.validateTransfer.mockResolvedValue({
      success: true,
      data: {
        is_valid: true,
        fee: 1.0,
        total_amount: 101.0,
        remaining_balance: 899.0,
        transfer_type: 'bank_to_ewallet'
      }
    })

    wrapper = mount(Transfer, {
      global: {
        plugins: [router]
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('组件初始化', () => {
    it('应该正确渲染转账页面', () => {
      expect(wrapper.find('.transfer-page').exists()).toBe(true)
      expect(wrapper.find('van-nav-bar').exists()).toBe(true)
      expect(wrapper.find('.progress-section').exists()).toBe(true)
    })

    it('应该显示进度指示器', () => {
      const steps = wrapper.find('van-steps')
      expect(steps.exists()).toBe(true)
      expect(steps.props('active')).toBe(0)
    })
  })

  describe('账户选择', () => {
    it('应该显示转出账户选择器', async () => {
      const fromAccountField = wrapper.find('[data-test="from-account-field"]')
      expect(fromAccountField.exists()).toBe(true)
      
      await fromAccountField.trigger('click')
      expect(wrapper.vm.showFromAccountPicker).toBe(true)
    })

    it('应该根据转出账户货币筛选转入账户', async () => {
      // 选择MYR账户作为转出账户
      wrapper.vm.form.fromAccountId = 'account1'
      await wrapper.vm.$nextTick()

      const toAccountOptions = wrapper.vm.toAccountColumns
      expect(toAccountOptions).toHaveLength(1) // 只有一个MYR的电子钱包账户
      expect(toAccountOptions[0].value).toBe('account2')
    })

    it('应该排除转出账户本身', async () => {
      wrapper.vm.form.fromAccountId = 'account1'
      await wrapper.vm.$nextTick()

      const toAccountOptions = wrapper.vm.toAccountColumns
      const fromAccountInOptions = toAccountOptions.find(option => option.value === 'account1')
      expect(fromAccountInOptions).toBeUndefined()
    })
  })

  describe('金额输入和验证', () => {
    it('应该在输入金额时触发验证', async () => {
      // 设置账户
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      
      // 输入金额
      const amountField = wrapper.find('[data-test="amount-field"]')
      await amountField.setValue('100')
      
      // 等待防抖
      await new Promise(resolve => setTimeout(resolve, 600))
      
      expect(transfersAPI.validateTransfer).toHaveBeenCalledWith({
        from_account_id: 'account1',
        to_account_id: 'account2',
        amount: 100
      })
    })

    it('应该显示手续费信息', async () => {
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '100'
      wrapper.vm.feeInfo.fee = 1.0
      wrapper.vm.feeInfo.totalAmount = 101.0
      
      await wrapper.vm.$nextTick()
      
      const feeSection = wrapper.find('[data-test="fee-section"]')
      expect(feeSection.exists()).toBe(true)
      expect(feeSection.text()).toContain('1.00')
      expect(feeSection.text()).toContain('101.00')
    })

    it('应该验证余额充足性', () => {
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '100'
      wrapper.vm.feeInfo.totalAmount = 101.0
      
      expect(wrapper.vm.isFormValid).toBe(true)
      
      // 设置超过余额的金额
      wrapper.vm.feeInfo.totalAmount = 1500.0
      expect(wrapper.vm.isFormValid).toBe(false)
    })
  })

  describe('表单提交', () => {
    it('应该在表单有效时启用提交按钮', async () => {
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '100'
      wrapper.vm.feeInfo.totalAmount = 101.0
      
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('[data-test="submit-button"]')
      expect(submitButton.props('disabled')).toBe(false)
    })

    it('应该在表单无效时禁用提交按钮', async () => {
      // 不设置必填字段
      await wrapper.vm.$nextTick()
      
      const submitButton = wrapper.find('[data-test="submit-button"]')
      expect(submitButton.props('disabled')).toBe(true)
    })

    it('应该在提交时跳转到确认页面', async () => {
      const routerPush = jest.spyOn(wrapper.vm.$router, 'push')
      
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '100'
      wrapper.vm.feeInfo.fee = 1.0
      wrapper.vm.feeInfo.totalAmount = 101.0
      wrapper.vm.feeInfo.remainingBalance = 899.0
      
      await wrapper.vm.handleSubmit()
      
      expect(routerPush).toHaveBeenCalledWith({
        name: 'TransferConfirm',
        query: {
          data: expect.any(String)
        }
      })
    })
  })

  describe('错误处理', () => {
    it('应该处理API验证错误', async () => {
      transfersAPI.validateTransfer.mockRejectedValue(new Error('API Error'))
      
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '100'
      
      await wrapper.vm.validateTransfer()
      
      // 应该重置费用信息
      expect(wrapper.vm.feeInfo.fee).toBe(0)
      expect(wrapper.vm.feeInfo.totalAmount).toBe(0)
    })

    it('应该显示余额不足错误', async () => {
      transfersAPI.validateTransfer.mockResolvedValue({
        success: false,
        error: '账户余额不足'
      })
      
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      wrapper.vm.form.amount = '2000'
      
      await wrapper.vm.validateTransfer()
      
      // 应该重置费用信息
      expect(wrapper.vm.feeInfo.fee).toBe(0)
    })
  })

  describe('用户体验', () => {
    it('应该在选择转出账户后重置转入账户', async () => {
      wrapper.vm.form.fromAccountId = 'account1'
      wrapper.vm.form.toAccountId = 'account2'
      
      // 更改转出账户
      wrapper.vm.form.fromAccountId = 'account3'
      await wrapper.vm.$nextTick()
      
      // 转入账户应该被重置
      expect(wrapper.vm.form.toAccountId).toBe('')
    })

    it('应该显示货币符号', () => {
      wrapper.vm.form.fromAccountId = 'account1'
      
      const currencySymbol = wrapper.find('.currency-symbol')
      expect(currencySymbol.exists()).toBe(true)
      expect(currencySymbol.text()).toContain('RM') // MYR的符号
    })

    it('应该显示账户余额', async () => {
      wrapper.vm.form.fromAccountId = 'account1'
      await wrapper.vm.$nextTick()
      
      const balanceCell = wrapper.find('[data-test="balance-cell"]')
      expect(balanceCell.exists()).toBe(true)
      expect(balanceCell.text()).toContain('1,000.00')
    })
  })

  describe('模板支持', () => {
    it('应该从路由参数加载模板数据', async () => {
      const mockTemplate = {
        id: 'template1',
        from_account: { id: 'account1' },
        to_account: { id: 'account2' },
        amount: 200,
        description: '模板转账'
      }

      transfersAPI.getTemplates = jest.fn().mockResolvedValue({
        success: true,
        data: {
          templates: [mockTemplate]
        }
      })

      transfersAPI.useTemplate = jest.fn().mockResolvedValue({ success: true })

      // 模拟路由参数
      wrapper.vm.$route.query.template_id = 'template1'
      
      await wrapper.vm.loadTemplate('template1')
      
      expect(wrapper.vm.form.fromAccountId).toBe('account1')
      expect(wrapper.vm.form.toAccountId).toBe('account2')
      expect(wrapper.vm.form.amount).toBe('200')
      expect(wrapper.vm.form.description).toBe('模板转账')
    })
  })
})
