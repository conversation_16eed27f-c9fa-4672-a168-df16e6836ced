<template>
  <div class="transfer-page">
    <van-nav-bar
      title="转账"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="bookmark-o" @click="$router.push('/transfer/templates')" />
      </template>
    </van-nav-bar>

    <!-- 进度指示器 -->
    <div class="progress-section">
      <van-steps :active="0" direction="horizontal">
        <van-step>填写信息</van-step>
        <van-step>确认转账</van-step>
        <van-step>转账完成</van-step>
      </van-steps>
    </div>

    <div class="transfer-content">
      <van-form @submit="handleSubmit">
        <!-- 转出账户选择 -->
        <van-cell-group inset title="转出账户">
          <van-field
            v-model="selectedFromAccountName"
            label="转出账户"
            placeholder="请选择转出账户"
            readonly
            is-link
            @click="showFromAccountPicker = true"
            :rules="[{ required: true, message: '请选择转出账户' }]"
          >
            <template #right-icon>
              <van-icon name="arrow" />
            </template>
          </van-field>
          
          <!-- 账户余额显示 -->
          <van-cell
            v-if="selectedFromAccount"
            title="可用余额"
            :value="formatCurrency(fromAccountBalance, selectedFromAccount.currency)"
            :label="`${selectedFromAccount.name} (${formatAccountType(selectedFromAccount.type)})`"
          />
        </van-cell-group>

        <!-- 转入账户选择 -->
        <van-cell-group inset title="转入账户">
          <van-field
            v-model="selectedToAccountName"
            label="转入账户"
            placeholder="请选择转入账户"
            readonly
            is-link
            @click="showToAccountPicker = true"
            :rules="[{ required: true, message: '请选择转入账户' }]"
          >
            <template #right-icon>
              <van-icon name="arrow" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 转账金额 -->
        <van-cell-group inset title="转账信息">
          <van-field
            v-model="form.amount"
            type="number"
            label="转账金额"
            placeholder="请输入转账金额"
            :rules="[
              { required: true, message: '请输入转账金额' },
              { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效金额' }
            ]"
            @input="onAmountChange"
          >
            <template #left-icon>
              <span class="currency-symbol">{{ getCurrencySymbol(transferCurrency) }}</span>
            </template>
          </van-field>

          <van-field
            v-model="form.description"
            label="转账备注"
            placeholder="请输入转账备注（可选）"
            maxlength="100"
            show-word-limit
          />
        </van-cell-group>

        <!-- 费用明细 -->
        <van-cell-group v-if="feeInfo.fee > 0" inset title="费用明细">
          <van-cell title="转账金额" :value="formatCurrency(parseFloat(form.amount || 0), transferCurrency)" />
          <van-cell title="手续费" :value="formatCurrency(feeInfo.fee, transferCurrency)" />
          <van-cell 
            title="总扣除金额" 
            :value="formatCurrency(feeInfo.totalAmount, transferCurrency)"
            :label="feeInfo.feeDescription"
            class="total-amount"
          />
          <van-cell 
            v-if="feeInfo.remainingBalance !== null"
            title="转账后余额" 
            :value="formatCurrency(feeInfo.remainingBalance, transferCurrency)"
          />
        </van-cell-group>

        <!-- 转账限额提示 -->
        <van-cell-group v-if="limitInfo.dailyLimit" inset title="转账限额">
          <van-cell 
            title="日转账限额" 
            :value="formatCurrency(limitInfo.dailyLimit, transferCurrency)"
          />
          <van-cell 
            v-if="limitInfo.remainingDailyLimit !== null"
            title="今日剩余额度" 
            :value="formatCurrency(limitInfo.remainingDailyLimit, transferCurrency)"
          />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            block
            native-type="submit"
            :loading="loading"
            :disabled="!isFormValid"
          >
            <van-icon name="arrow" />
            下一步
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 转出账户选择器 -->
    <van-popup v-model:show="showFromAccountPicker" position="bottom" round>
      <van-picker
        :columns="fromAccountColumns"
        @confirm="onFromAccountConfirm"
        @cancel="showFromAccountPicker = false"
        title="选择转出账户"
      />
    </van-popup>

    <!-- 转入账户选择器 -->
    <van-popup v-model:show="showToAccountPicker" position="bottom" round>
      <van-picker
        :columns="toAccountColumns"
        @confirm="onToAccountConfirm"
        @cancel="showToAccountPicker = false"
        title="选择转入账户"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { transfersAPI } from '@/api/transfers'
import { showToast, showConfirmDialog } from 'vant'
import { formatCurrency, formatAccountType } from '@/utils/format'
import { getCurrencySymbol } from '@/config/currencies'

const router = useRouter()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showFromAccountPicker = ref(false)
const showToAccountPicker = ref(false)

// 表单数据
const form = reactive({
  fromAccountId: '',
  toAccountId: '',
  amount: '',
  description: ''
})

// 费用和限额信息
const feeInfo = reactive({
  fee: 0,
  totalAmount: 0,
  remainingBalance: null,
  feeDescription: ''
})

const limitInfo = reactive({
  dailyLimit: null,
  remainingDailyLimit: null
})

// 转账配置
const transferConfig = ref({})

// 计算属性
const selectedFromAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === form.fromAccountId)
})

const selectedToAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === form.toAccountId)
})

const selectedFromAccountName = computed(() => {
  if (!selectedFromAccount.value) return ''
  return `${selectedFromAccount.value.name} (${formatAccountType(selectedFromAccount.value.type)})`
})

const selectedToAccountName = computed(() => {
  if (!selectedToAccount.value) return ''
  return `${selectedToAccount.value.name} (${formatAccountType(selectedToAccount.value.type)})`
})

const transferCurrency = computed(() => {
  return selectedFromAccount.value?.currency || 'MYR'
})

const fromAccountBalance = computed(() => {
  if (!selectedFromAccount.value) return 0
  return selectedFromAccount.value.current_balance || selectedFromAccount.value.initial_balance || 0
})

// 转出账户选择器选项
const fromAccountColumns = computed(() => {
  return accountsStore.accounts
    .filter(account => account.type !== 'investment' || account.name === 'KDI') // 排除投资账户，但保留KDI
    .map(account => ({
      text: `${account.name} (${formatAccountType(account.type)}) - ${formatCurrency(account.current_balance || account.initial_balance, account.currency)}`,
      value: account.id
    }))
})

// 转入账户选择器选项（根据转出账户的货币筛选）
const toAccountColumns = computed(() => {
  if (!selectedFromAccount.value) return []
  
  return accountsStore.accounts
    .filter(account => 
      account.currency === selectedFromAccount.value.currency && // 相同货币
      account.id !== form.fromAccountId && // 不能是转出账户本身
      account.type !== 'investment' // 排除投资账户
    )
    .map(account => ({
      text: `${account.name} (${formatAccountType(account.type)})`,
      value: account.id
    }))
})

const isFormValid = computed(() => {
  return form.fromAccountId && 
         form.toAccountId && 
         form.amount && 
         parseFloat(form.amount) > 0 &&
         feeInfo.totalAmount <= fromAccountBalance.value
})

// 方法
const onFromAccountConfirm = ({ selectedOptions }) => {
  form.fromAccountId = selectedOptions[0]?.value || ''
  // 重置转入账户选择
  form.toAccountId = ''
  // 重置费用信息
  resetFeeInfo()
  showFromAccountPicker.value = false
}

const onToAccountConfirm = ({ selectedOptions }) => {
  form.toAccountId = selectedOptions[0]?.value || ''
  showToAccountPicker.value = false
  // 如果金额已输入，重新计算费用
  if (form.amount) {
    validateTransfer()
  }
}

const onAmountChange = () => {
  // 防抖处理
  clearTimeout(onAmountChange.timer)
  onAmountChange.timer = setTimeout(() => {
    if (form.amount && form.fromAccountId && form.toAccountId) {
      validateTransfer()
    } else {
      resetFeeInfo()
    }
  }, 500)
}

const resetFeeInfo = () => {
  feeInfo.fee = 0
  feeInfo.totalAmount = 0
  feeInfo.remainingBalance = null
  feeInfo.feeDescription = ''
}

const validateTransfer = async () => {
  if (!form.amount || !form.fromAccountId || !form.toAccountId) return
  
  try {
    const response = await transfersAPI.validateTransfer({
      from_account_id: form.fromAccountId,
      to_account_id: form.toAccountId,
      amount: parseFloat(form.amount)
    })
    
    if (response.success) {
      const data = response.data
      feeInfo.fee = data.fee
      feeInfo.totalAmount = data.total_amount
      feeInfo.remainingBalance = data.remaining_balance
      
      // 设置费用描述
      const config = transferConfig.value[data.transfer_type]
      if (config) {
        if (config.fee_type === 'fixed') {
          feeInfo.feeDescription = `固定手续费 ${formatCurrency(config.fee_amount, transferCurrency.value)}`
        } else if (config.fee_type === 'percentage') {
          feeInfo.feeDescription = `手续费率 ${(config.fee_rate * 100).toFixed(3)}%，最低 ${formatCurrency(config.fee_min, transferCurrency.value)}，最高 ${formatCurrency(config.fee_max, transferCurrency.value)}`
        }
      }
      
      // 设置限额信息
      if (data.remaining_daily_limit !== null) {
        limitInfo.remainingDailyLimit = data.remaining_daily_limit
      }
    }
  } catch (error) {
    console.error('验证转账失败:', error)
    resetFeeInfo()
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    showToast('请完善转账信息')
    return
  }

  // 准备转账数据
  const transferData = {
    fromAccountId: form.fromAccountId,
    toAccountId: form.toAccountId,
    amount: parseFloat(form.amount),
    currency: transferCurrency.value,
    description: form.description.trim(),
    fee: feeInfo.fee,
    totalAmount: feeInfo.totalAmount,
    currentBalance: fromAccountBalance.value,
    remainingBalance: feeInfo.remainingBalance,
    transferType: 'bank_to_bank' // 这里应该根据实际账户类型确定
  }

  // 跳转到确认页面
  router.push({
    name: 'TransferConfirm',
    query: {
      data: encodeURIComponent(JSON.stringify(transferData))
    }
  })
}

// 生命周期
onMounted(async () => {
  // 加载账户数据
  try {
    await accountsStore.fetchAccounts()

    // 加载转账配置
    const configResponse = await transfersAPI.getConfig()
    if (configResponse.success) {
      transferConfig.value = configResponse.data.transfer_types

      // 设置限额信息
      Object.values(transferConfig.value).forEach(config => {
        if (config.daily_limit) {
          limitInfo.dailyLimit = config.daily_limit
        }
      })
    }

    // 如果有模板ID，加载模板数据
    const templateId = route.query.template_id
    if (templateId) {
      await loadTemplate(templateId)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载数据失败')
  }
})

const loadTemplate = async (templateId) => {
  try {
    const response = await transfersAPI.getTemplates()
    if (response.success) {
      const template = response.data.templates.find(t => t.id === templateId)
      if (template) {
        // 填充表单数据
        form.fromAccountId = template.from_account.id
        form.toAccountId = template.to_account.id
        form.amount = template.amount ? template.amount.toString() : ''
        form.description = template.description || ''

        // 更新模板使用统计
        await transfersAPI.useTemplate(templateId)

        // 如果有金额，验证转账
        if (form.amount) {
          validateTransfer()
        }
      }
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    showToast('加载模板失败')
  }
}

// 监听转出账户变化，重置转入账户
watch(() => form.fromAccountId, () => {
  if (form.toAccountId) {
    // 检查转入账户是否仍然有效
    const isValidToAccount = toAccountColumns.value.some(option => option.value === form.toAccountId)
    if (!isValidToAccount) {
      form.toAccountId = ''
      resetFeeInfo()
    }
  }
})
</script>

<style scoped>
.transfer-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.transfer-content {
  padding: 16px;
}

.currency-symbol {
  font-weight: bold;
  color: #1989fa;
  margin-right: 4px;
}

.total-amount {
  font-weight: bold;
}

.total-amount .van-cell__value {
  color: #ee0a24;
  font-weight: bold;
}

.submit-section {
  margin-top: 24px;
  padding: 0 16px;
}

.submit-section .van-button {
  height: 50px;
  font-size: 16px;
  font-weight: bold;
}

.progress-section {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}
</style>
