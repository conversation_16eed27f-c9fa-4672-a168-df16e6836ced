"""
转账模板功能测试用例
测试转账模板的创建、更新、删除和使用功能
"""

import pytest
import json
import uuid
from datetime import datetime
from web.app import app, init_db


@pytest.fixture
def client():
    """创建测试客户端"""
    app.config['TESTING'] = True
    app.config['DATABASE'] = ':memory:'
    
    with app.test_client() as client:
        with app.app_context():
            init_db()
            yield client


@pytest.fixture
def auth_headers(client):
    """创建认证头部"""
    user_data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    client.post('/api/auth/register', 
                data=json.dumps(user_data),
                content_type='application/json')
    
    login_response = client.post('/api/auth/login',
                                data=json.dumps({
                                    'username': 'testuser',
                                    'password': 'testpass123'
                                }),
                                content_type='application/json')
    
    token = json.loads(login_response.data)['token']
    return {'Authorization': f'Bearer {token}'}


@pytest.fixture
def test_accounts(client, auth_headers):
    """创建测试账户"""
    accounts = []
    
    bank_account = {
        'name': 'Test Bank Account',
        'type': 'bank',
        'currency': 'MYR',
        'initial_balance': 1000.0
    }
    response = client.post('/api/accounts/',
                          data=json.dumps(bank_account),
                          content_type='application/json',
                          headers=auth_headers)
    accounts.append(json.loads(response.data)['data'])
    
    ewallet_account = {
        'name': 'Test eWallet',
        'type': 'ewallet',
        'currency': 'MYR',
        'initial_balance': 500.0
    }
    response = client.post('/api/accounts/',
                          data=json.dumps(ewallet_account),
                          content_type='application/json',
                          headers=auth_headers)
    accounts.append(json.loads(response.data)['data'])
    
    return accounts


class TestTransferTemplateCreation:
    """转账模板创建测试"""
    
    def test_create_template_success(self, client, auth_headers, test_accounts):
        """测试创建转账模板成功"""
        bank_account, ewallet_account = test_accounts
        
        template_data = {
            'template_name': '给朋友转账',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'description': '朋友聚餐费用'
        }
        
        response = client.post('/api/transfers/templates',
                              data=json.dumps(template_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'template_id' in data['data']
    
    def test_create_template_missing_fields(self, client, auth_headers, test_accounts):
        """测试创建模板缺少必填字段"""
        template_data = {
            'template_name': '测试模板'
            # 缺少账户信息
        }
        
        response = client.post('/api/transfers/templates',
                              data=json.dumps(template_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert '缺少必填字段' in data['error']
    
    def test_create_template_duplicate_name(self, client, auth_headers, test_accounts):
        """测试创建重复名称的模板"""
        bank_account, ewallet_account = test_accounts
        
        template_data = {
            'template_name': '重复模板',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id']
        }
        
        # 创建第一个模板
        client.post('/api/transfers/templates',
                   data=json.dumps(template_data),
                   content_type='application/json',
                   headers=auth_headers)
        
        # 尝试创建同名模板
        response = client.post('/api/transfers/templates',
                              data=json.dumps(template_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert '已存在' in data['error']


class TestTransferTemplateRetrieval:
    """转账模板获取测试"""
    
    def test_get_templates_empty(self, client, auth_headers):
        """测试获取空模板列表"""
        response = client.get('/api/transfers/templates', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['templates'] == []
    
    def test_get_templates_with_data(self, client, auth_headers, test_accounts):
        """测试获取包含数据的模板列表"""
        bank_account, ewallet_account = test_accounts
        
        # 创建模板
        template_data = {
            'template_name': '测试模板',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'description': '测试描述'
        }
        
        client.post('/api/transfers/templates',
                   data=json.dumps(template_data),
                   content_type='application/json',
                   headers=auth_headers)
        
        # 获取模板列表
        response = client.get('/api/transfers/templates', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['templates']) == 1
        
        template = data['data']['templates'][0]
        assert template['template_name'] == '测试模板'
        assert template['amount'] == 100.0
        assert template['description'] == '测试描述'
        assert template['usage_count'] == 0
        assert template['is_favorite'] is False


class TestTransferTemplateUpdate:
    """转账模板更新测试"""
    
    def test_update_template_success(self, client, auth_headers, test_accounts):
        """测试更新转账模板成功"""
        bank_account, ewallet_account = test_accounts
        
        # 创建模板
        template_data = {
            'template_name': '原始模板',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0
        }
        
        create_response = client.post('/api/transfers/templates',
                                     data=json.dumps(template_data),
                                     content_type='application/json',
                                     headers=auth_headers)
        
        template_id = json.loads(create_response.data)['data']['template_id']
        
        # 更新模板
        update_data = {
            'template_name': '更新后的模板',
            'amount': 200.0,
            'is_favorite': True
        }
        
        response = client.put(f'/api/transfers/templates/{template_id}',
                             data=json.dumps(update_data),
                             content_type='application/json',
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
    
    def test_update_nonexistent_template(self, client, auth_headers):
        """测试更新不存在的模板"""
        fake_id = str(uuid.uuid4())
        
        update_data = {
            'template_name': '不存在的模板'
        }
        
        response = client.put(f'/api/transfers/templates/{fake_id}',
                             data=json.dumps(update_data),
                             content_type='application/json',
                             headers=auth_headers)
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['success'] is False
        assert '不存在' in data['error']


class TestTransferTemplateUsage:
    """转账模板使用测试"""
    
    def test_use_template_success(self, client, auth_headers, test_accounts):
        """测试使用转账模板成功"""
        bank_account, ewallet_account = test_accounts
        
        # 创建模板
        template_data = {
            'template_name': '使用测试模板',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0
        }
        
        create_response = client.post('/api/transfers/templates',
                                     data=json.dumps(template_data),
                                     content_type='application/json',
                                     headers=auth_headers)
        
        template_id = json.loads(create_response.data)['data']['template_id']
        
        # 使用模板
        response = client.post(f'/api/transfers/templates/{template_id}/use',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        
        # 验证使用次数是否增加
        get_response = client.get('/api/transfers/templates', headers=auth_headers)
        templates = json.loads(get_response.data)['data']['templates']
        used_template = next(t for t in templates if t['id'] == template_id)
        assert used_template['usage_count'] == 1
        assert used_template['last_used_at'] is not None


class TestTransferTemplateDeletion:
    """转账模板删除测试"""
    
    def test_delete_template_success(self, client, auth_headers, test_accounts):
        """测试删除转账模板成功"""
        bank_account, ewallet_account = test_accounts
        
        # 创建模板
        template_data = {
            'template_name': '待删除模板',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id']
        }
        
        create_response = client.post('/api/transfers/templates',
                                     data=json.dumps(template_data),
                                     content_type='application/json',
                                     headers=auth_headers)
        
        template_id = json.loads(create_response.data)['data']['template_id']
        
        # 删除模板
        response = client.delete(f'/api/transfers/templates/{template_id}',
                                headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        
        # 验证模板已被删除
        get_response = client.get('/api/transfers/templates', headers=auth_headers)
        templates = json.loads(get_response.data)['data']['templates']
        assert len(templates) == 0
    
    def test_delete_nonexistent_template(self, client, auth_headers):
        """测试删除不存在的模板"""
        fake_id = str(uuid.uuid4())
        
        response = client.delete(f'/api/transfers/templates/{fake_id}',
                                headers=auth_headers)
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['success'] is False
        assert '不存在' in data['error']


class TestTransferTemplateIntegration:
    """转账模板集成测试"""
    
    def test_template_workflow(self, client, auth_headers, test_accounts):
        """测试完整的模板工作流程"""
        bank_account, ewallet_account = test_accounts
        
        # 1. 创建模板
        template_data = {
            'template_name': '完整流程测试',
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 150.0,
            'description': '完整流程测试描述'
        }
        
        create_response = client.post('/api/transfers/templates',
                                     data=json.dumps(template_data),
                                     content_type='application/json',
                                     headers=auth_headers)
        
        template_id = json.loads(create_response.data)['data']['template_id']
        
        # 2. 使用模板多次
        for _ in range(3):
            client.post(f'/api/transfers/templates/{template_id}/use',
                       headers=auth_headers)
        
        # 3. 更新模板为收藏
        client.put(f'/api/transfers/templates/{template_id}',
                  data=json.dumps({'is_favorite': True}),
                  content_type='application/json',
                  headers=auth_headers)
        
        # 4. 验证最终状态
        get_response = client.get('/api/transfers/templates', headers=auth_headers)
        templates = json.loads(get_response.data)['data']['templates']
        template = templates[0]
        
        assert template['usage_count'] == 3
        assert template['is_favorite'] is True
        assert template['template_name'] == '完整流程测试'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
