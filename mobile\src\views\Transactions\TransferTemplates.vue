<template>
  <div class="transfer-templates-page">
    <van-nav-bar
      title="转账模板"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="plus" @click="addTemplate" />
      </template>
    </van-nav-bar>

    <div class="templates-content">
      <!-- 搜索栏 -->
      <div class="search-section">
        <van-search
          v-model="searchKeyword"
          placeholder="搜索模板名称..."
          @search="onSearch"
          @clear="onSearchClear"
        />
      </div>

      <!-- 筛选器 -->
      <div class="filter-section">
        <van-dropdown-menu>
          <van-dropdown-item v-model="filterType" :options="filterOptions" />
        </van-dropdown-menu>
      </div>

      <!-- 模板列表 -->
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="filteredTemplates.length === 0" class="empty-container">
        <div class="empty-icon">📋</div>
        <p>{{ searchKeyword ? '未找到相关模板' : '暂无转账模板' }}</p>
        <van-button 
          type="primary" 
          size="small"
          @click="addTemplate"
        >
          创建模板
        </van-button>
      </div>
      
      <van-cell-group v-else inset>
        <van-swipe-cell
          v-for="template in filteredTemplates"
          :key="template.id"
        >
          <van-cell
            :title="template.template_name"
            :label="getTemplateLabel(template)"
            :value="template.amount ? formatCurrency(template.amount, template.from_account.currency) : ''"
            is-link
            @click="useTemplate(template)"
          >
            <template #icon>
              <div class="template-icon">
                <van-icon 
                  :name="template.is_favorite ? 'star' : 'star-o'" 
                  :color="template.is_favorite ? '#ffd21e' : '#c8c9cc'"
                />
              </div>
            </template>
            <template #right-icon>
              <div class="template-info">
                <span class="usage-count">使用 {{ template.usage_count }} 次</span>
                <van-icon name="arrow" />
              </div>
            </template>
          </van-cell>
          
          <template #right>
            <van-button 
              square 
              type="primary" 
              text="收藏"
              @click="toggleFavorite(template)"
            />
            <van-button 
              square 
              type="warning" 
              text="编辑"
              @click="editTemplate(template)"
            />
            <van-button 
              square 
              type="danger" 
              text="删除"
              @click="deleteTemplate(template)"
            />
          </template>
        </van-swipe-cell>
      </van-cell-group>

      <!-- 统计信息 -->
      <div v-if="templates.length > 0" class="stats-section">
        <van-cell-group inset title="统计信息">
          <van-cell title="总模板数" :value="templates.length" />
          <van-cell title="收藏模板" :value="favoriteCount" />
          <van-cell title="最常用模板" :value="mostUsedTemplate?.template_name || '无'" />
        </van-cell-group>
      </div>
    </div>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      :gap="{ x: 24, y: 80 }"
      @click="addTemplate"
    />

    <!-- 模板详情弹窗 -->
    <van-popup 
      v-model:show="showTemplateDetail" 
      position="bottom" 
      round
      :style="{ height: '60%' }"
    >
      <div class="template-detail">
        <div class="detail-header">
          <h3>{{ selectedTemplate?.template_name }}</h3>
          <van-icon name="cross" @click="showTemplateDetail = false" />
        </div>
        
        <van-cell-group v-if="selectedTemplate">
          <van-cell title="转出账户" :value="selectedTemplate.from_account.name" />
          <van-cell title="转入账户" :value="selectedTemplate.to_account.name" />
          <van-cell 
            title="默认金额" 
            :value="selectedTemplate.amount ? formatCurrency(selectedTemplate.amount, selectedTemplate.from_account.currency) : '无'"
          />
          <van-cell title="备注" :value="selectedTemplate.description || '无'" />
          <van-cell title="使用次数" :value="selectedTemplate.usage_count" />
          <van-cell 
            title="最后使用" 
            :value="selectedTemplate.last_used_at ? formatDate(selectedTemplate.last_used_at) : '从未使用'"
          />
        </van-cell-group>
        
        <div class="detail-actions">
          <van-button 
            type="primary" 
            block 
            @click="useSelectedTemplate"
          >
            使用此模板
          </van-button>
          <van-button 
            block 
            @click="editSelectedTemplate"
          >
            编辑模板
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { transfersAPI } from '@/api/transfers'
import { showToast, showConfirmDialog } from 'vant'
import { formatCurrency, formatDate } from '@/utils/format'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const filterType = ref('all')
const templates = ref([])
const showTemplateDetail = ref(false)
const selectedTemplate = ref(null)

// 筛选选项
const filterOptions = [
  { text: '全部模板', value: 'all' },
  { text: '收藏模板', value: 'favorite' },
  { text: '常用模板', value: 'frequent' }
]

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value

  // 搜索过滤
  if (searchKeyword.value) {
    result = result.filter(template => 
      template.template_name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      template.from_account.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      template.to_account.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 类型过滤
  if (filterType.value === 'favorite') {
    result = result.filter(template => template.is_favorite)
  } else if (filterType.value === 'frequent') {
    result = result.filter(template => template.usage_count > 0)
  }

  return result
})

const favoriteCount = computed(() => {
  return templates.value.filter(template => template.is_favorite).length
})

const mostUsedTemplate = computed(() => {
  return templates.value.reduce((max, template) => 
    template.usage_count > (max?.usage_count || 0) ? template : max, null
  )
})

// 方法
const getTemplateLabel = (template) => {
  return `${template.from_account.name} → ${template.to_account.name}`
}

const onSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const onSearchClear = () => {
  searchKeyword.value = ''
}

const addTemplate = () => {
  router.push('/transfer/template/add')
}

const useTemplate = (template) => {
  // 使用模板进行转账
  router.push({
    name: 'Transfer',
    query: {
      template_id: template.id
    }
  })
}

const editTemplate = (template) => {
  router.push(`/transfer/template/edit/${template.id}`)
}

const toggleFavorite = async (template) => {
  try {
    await transfersAPI.updateTemplate(template.id, {
      is_favorite: !template.is_favorite
    })
    
    template.is_favorite = !template.is_favorite
    showToast(template.is_favorite ? '已添加到收藏' : '已取消收藏')
  } catch (error) {
    console.error('更新收藏状态失败:', error)
    showToast('操作失败')
  }
}

const deleteTemplate = async (template) => {
  const confirmed = await showConfirmDialog({
    title: '确认删除',
    message: `确定要删除模板"${template.template_name}"吗？`,
    confirmButtonText: '删除',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (!confirmed) return

  try {
    await transfersAPI.deleteTemplate(template.id)
    
    // 从列表中移除
    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
    }
    
    showToast('模板已删除')
  } catch (error) {
    console.error('删除模板失败:', error)
    showToast('删除失败')
  }
}

const useSelectedTemplate = () => {
  if (selectedTemplate.value) {
    useTemplate(selectedTemplate.value)
  }
  showTemplateDetail.value = false
}

const editSelectedTemplate = () => {
  if (selectedTemplate.value) {
    editTemplate(selectedTemplate.value)
  }
  showTemplateDetail.value = false
}

const loadTemplates = async () => {
  loading.value = true
  
  try {
    const response = await transfersAPI.getTemplates()
    
    if (response.success) {
      templates.value = response.data.templates
    } else {
      showToast('加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    showToast('加载模板失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.transfer-templates-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.templates-content {
  padding: 16px;
}

.search-section {
  margin-bottom: 16px;
}

.filter-section {
  margin-bottom: 16px;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.template-icon {
  margin-right: 12px;
}

.template-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-count {
  font-size: 12px;
  color: #969799;
}

.stats-section {
  margin-top: 24px;
}

.template-detail {
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.detail-actions {
  margin-top: 24px;
}

.detail-actions .van-button {
  margin-bottom: 12px;
}
</style>
