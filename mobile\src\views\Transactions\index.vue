<template>
  <div class="transactions-page">
    <van-nav-bar
      title="交易记录"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <div class="nav-actions">
          <van-icon name="search" size="18" @click="showSearchBar = true" class="nav-icon" />
          <van-icon name="exchange" size="18" @click="$router.push('/transfer')" class="nav-icon" title="转账" />
          <van-icon name="ellipsis" size="18" @click="showActionSheet = true" class="nav-icon" />
          <van-icon name="plus" size="18" @click="$router.push('/transaction/add')" class="nav-icon" />
        </div>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 搜索栏 -->
      <div v-if="showSearchBar" class="search-section">
        <van-search
          v-model="searchKeyword"
          placeholder="搜索交易描述、分类..."
          show-action
          @search="onSearch"
          @cancel="onSearchCancel"
          @clear="onSearchClear"
        >
          <template #action>
            <div @click="onSearchCancel">取消</div>
          </template>
        </van-search>
      </div>

      <!-- 筛选器 -->
      <div class="filter-section">
        <van-dropdown-menu>
          <van-dropdown-item v-model="filters.type" :options="typeOptions" />
          <van-dropdown-item v-model="filters.category" :options="categoryOptions" />
          <van-dropdown-item v-model="filters.currency" :options="currencyOptions" />
        </van-dropdown-menu>
      </div>

      <!-- 搜索结果提示 -->
      <div v-if="searchKeyword && filteredTransactions.length > 0" class="search-result-tip">
        <span>找到 {{ filteredTransactions.length }} 条相关记录</span>
        <van-button type="primary" size="mini" plain @click="clearSearch">
          清除搜索
        </van-button>
      </div>

      <!-- 交易列表 -->
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="filteredTransactions.length === 0" class="empty-container">
        <div class="empty-icon">📝</div>
        <p>暂无交易记录</p>
        <van-button 
          type="primary" 
          size="small"
          @click="$router.push('/transaction/add')"
        >
          立即记账
        </van-button>
      </div>
      
      <van-list v-else>
        <van-cell-group 
          v-for="group in groupedTransactions" 
          :key="group.date"
          :title="group.date"
          inset
        >
          <van-cell
            v-for="record in group.transactions"
            :key="record.id"
            :title="record.description"
            :label="getRecordLabel(record)"
            :value="formatCurrency(record.amount, record.currency)"
            :value-class="getRecordValueClass(record)"
            :is-link="!batchMode"
            @click="handleRecordClick(record)"
          >
            <template #icon>
              <van-checkbox
                v-if="batchMode && record.recordType === 'transaction'"
                :model-value="selectedTransactions.includes(record.id)"
                @update:model-value="(checked) => handleTransactionCheck(record.id, checked)"
                @click.stop
              />
              <div v-else class="transaction-icon" :class="record.recordType">
                {{ getRecordIcon(record) }}
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </van-list>
    </div>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      v-if="!batchMode"
      axis="xy"
      icon="plus"
      :gap="{ x: 24, y: 80 }"
      @click="$router.push('/transaction/add')"
    />

    <!-- 批量操作底部栏 -->
    <div v-if="batchMode" class="batch-toolbar">
      <div class="batch-info">
        <van-checkbox
          :model-value="selectAll"
          @update:model-value="onSelectAllChange"
        >
          全选 ({{ selectedTransactions.length }}/{{ filteredTransactions.length }})
        </van-checkbox>
      </div>
      <div class="batch-actions">
        <van-button type="danger" size="small" @click="batchDelete" :disabled="selectedTransactions.length === 0">
          删除
        </van-button>
        <van-button type="default" size="small" @click="exitBatchMode">
          取消
        </van-button>
      </div>
    </div>

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="actionSheetActions"
      @select="onActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import { formatCurrency, formatDate } from '@/utils/format'
import { getCurrencyOptionsForAccounts } from '@/config/currencies'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const transactionsStore = useTransactionsStore()

const loading = ref(false)
const showSearchBar = ref(false)
const searchKeyword = ref('')
const showActionSheet = ref(false)
const batchMode = ref(false)
const selectedTransactions = ref([])
const selectAll = ref(false)

// 筛选器
const filters = ref({
  type: 'all',
  category: 'all',
  currency: 'all'
})

// 筛选选项
const typeOptions = [
  { text: '全部类型', value: 'all' },
  { text: '收入', value: 'income' },
  { text: '支出', value: 'expense' },
  { text: '转账', value: 'transfer' }
]

const categoryOptions = [
  { text: '全部分类', value: 'all' },
  { text: '储蓄', value: '储蓄' },
  { text: '固定', value: '固定' },
  { text: '流动', value: '流动' },
  { text: '债务', value: '债务' }
]

// 货币筛选选项 - 使用统一配置
const currencyOptions = computed(() => {
  const options = [{ text: '全部货币', value: 'all' }]
  const currencies = getCurrencyOptionsForAccounts()

  currencies.forEach(currency => {
    options.push({
      text: `${currency.symbol} (${currency.code})`,
      value: currency.code
    })
  })

  return options
})

// 操作菜单选项
const actionSheetActions = [
  { name: '批量管理', value: 'batch' },
  { name: '导出数据', value: 'export' },
  { name: '统计分析', value: 'stats' }
]

// 合并交易和转账记录
const allRecords = computed(() => {
  const records = []

  // 添加普通交易记录
  transactionsStore.transactions.forEach(transaction => {
    records.push({
      ...transaction,
      recordType: 'transaction',
      sortDate: new Date(transaction.date)
    })
  })

  // 添加转账记录
  transactionsStore.transfers.forEach(transfer => {
    records.push({
      id: transfer.id,
      description: `转账：${transfer.from_account.name} → ${transfer.to_account.name}`,
      category: '转账',
      amount: transfer.amount,
      currency: transfer.currency,
      date: transfer.created_at.split('T')[0], // 提取日期部分
      type: 'transfer',
      recordType: 'transfer',
      sortDate: new Date(transfer.created_at),
      transferData: transfer
    })
  })

  return records.sort((a, b) => b.sortDate - a.sortDate)
})

// 筛选后的记录
const filteredTransactions = computed(() => {
  let records = allRecords.value

  // 搜索过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    records = records.filter(r =>
      r.description.toLowerCase().includes(keyword) ||
      r.category.toLowerCase().includes(keyword) ||
      (r.attachment && r.attachment.toLowerCase().includes(keyword))
    )
  }

  // 类型过滤
  if (filters.value.type !== 'all') {
    if (filters.value.type === 'transfer') {
      records = records.filter(r => r.recordType === 'transfer')
    } else {
      records = records.filter(r => r.recordType === 'transaction' && r.type === filters.value.type)
    }
  }

  // 分类过滤
  if (filters.value.category !== 'all') {
    records = records.filter(r => r.category === filters.value.category)
  }

  // 货币过滤
  if (filters.value.currency !== 'all') {
    records = records.filter(r => r.currency === filters.value.currency)
  }

  return records
})

// 按日期分组的交易
const groupedTransactions = computed(() => {
  const groups = {}
  
  filteredTransactions.value.forEach(transaction => {
    const date = formatDate(transaction.date)
    if (!groups[date]) {
      groups[date] = {
        date,
        transactions: []
      }
    }
    groups[date].transactions.push(transaction)
  })
  
  return Object.values(groups)
})

onMounted(async () => {
  await loadTransactions()
})

const loadTransactions = async () => {
  loading.value = true
  try {
    console.log('开始加载交易数据...')
    // 并行加载交易和转账数据
    await Promise.all([
      transactionsStore.fetchTransactions(),
      transactionsStore.fetchTransfers()
    ])
    console.log('交易数据加载完成:', transactionsStore.transactions)
    console.log('转账数据加载完成:', transactionsStore.transfers)
    console.log('交易数量:', transactionsStore.transactions.length)
    console.log('转账数量:', transactionsStore.transfers.length)
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const viewTransaction = (transaction) => {
  router.push(`/transaction/detail/${transaction.id}`)
}

// 搜索相关方法
const onSearch = (value) => {
  console.log('搜索:', value)
  // 搜索逻辑已在 computed 中处理
}

const onSearchCancel = () => {
  showSearchBar.value = false
  searchKeyword.value = ''
}

const onSearchClear = () => {
  searchKeyword.value = ''
}

const clearSearch = () => {
  searchKeyword.value = ''
  showSearchBar.value = false
}

// 记录显示相关方法
const getRecordLabel = (record) => {
  if (record.recordType === 'transfer') {
    return `${record.transferData.from_account.name} → ${record.transferData.to_account.name}`
  }
  return record.category
}

const getRecordValueClass = (record) => {
  if (record.recordType === 'transfer') {
    return 'amount transfer'
  }
  return record.type === 'income' ? 'amount income' : 'amount expense'
}

const getRecordIcon = (record) => {
  if (record.recordType === 'transfer') {
    return '🔄'
  }
  return record.type === 'income' ? '💰' : '💸'
}

// 批量操作相关方法
const handleRecordClick = (record) => {
  if (batchMode.value && record.recordType === 'transaction') {
    toggleTransactionSelection(record.id)
  } else {
    viewRecord(record)
  }
}

const handleTransactionClick = (transaction) => {
  if (batchMode.value) {
    toggleTransactionSelection(transaction.id)
  } else {
    viewTransaction(transaction)
  }
}

const viewRecord = (record) => {
  if (record.recordType === 'transfer') {
    // 跳转到转账详情页面
    router.push(`/transfer/detail/${record.id}`)
  } else {
    viewTransaction(record)
  }
}

const toggleTransactionSelection = (transactionId) => {
  const index = selectedTransactions.value.indexOf(transactionId)
  if (index > -1) {
    selectedTransactions.value.splice(index, 1)
  } else {
    selectedTransactions.value.push(transactionId)
  }

  // 更新全选状态，但不触发递归更新
  updateSelectAllState()
}

const updateSelectAllState = () => {
  const shouldSelectAll = selectedTransactions.value.length === filteredTransactions.value.length && filteredTransactions.value.length > 0
  if (selectAll.value !== shouldSelectAll) {
    selectAll.value = shouldSelectAll
  }
}

const handleTransactionCheck = (transactionId, checked) => {
  if (checked) {
    if (!selectedTransactions.value.includes(transactionId)) {
      selectedTransactions.value.push(transactionId)
    }
  } else {
    const index = selectedTransactions.value.indexOf(transactionId)
    if (index > -1) {
      selectedTransactions.value.splice(index, 1)
    }
  }
  updateSelectAllState()
}

const onSelectAllChange = (checked) => {
  if (checked) {
    selectedTransactions.value = filteredTransactions.value.map(t => t.id)
  } else {
    selectedTransactions.value = []
  }
}

const enterBatchMode = () => {
  batchMode.value = true
  selectedTransactions.value = []
  selectAll.value = false
}

const exitBatchMode = () => {
  batchMode.value = false
  selectedTransactions.value = []
  selectAll.value = false
}

const batchDelete = async () => {
  if (selectedTransactions.value.length === 0) {
    showToast('请选择要删除的交易')
    return
  }

  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除选中的 ${selectedTransactions.value.length} 条交易记录吗？此操作不可撤销。`
    })

    // 批量删除
    const deletePromises = selectedTransactions.value.map(id =>
      transactionsStore.deleteTransaction(id)
    )

    await Promise.all(deletePromises)

    showToast({
      message: `成功删除 ${selectedTransactions.value.length} 条交易记录`,
      type: 'success'
    })

    exitBatchMode()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      showToast({
        message: '删除失败，请重试',
        type: 'fail'
      })
    }
  }
}

const onActionSelect = (action) => {
  showActionSheet.value = false

  switch (action.value) {
    case 'batch':
      enterBatchMode()
      break
    case 'export':
      exportData()
      break
    case 'stats':
      showStats()
      break
  }
}

const exportData = () => {
  showToast('导出功能开发中...')
}

const showStats = () => {
  showToast('统计功能开发中...')
}
</script>

<style scoped>
.transactions-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-icon {
  cursor: pointer;
  transition: opacity 0.2s;
}

.nav-icon:active {
  opacity: 0.6;
}

.search-section {
  background: white;
  padding: 8px 0;
}

:deep(.van-search) {
  padding: 8px 16px;
}

:deep(.van-search__content) {
  background: #f7f8fa;
  border-radius: 20px;
}

:deep(.van-search__action) {
  color: #1989fa;
  font-size: 14px;
  margin-left: 8px;
}

.search-result-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fff3cd;
  border-left: 4px solid #ffc107;
  margin-bottom: 8px;
  font-size: 14px;
  color: #856404;
}

.filter-section {
  background: white;
  margin-bottom: 12px;
}

.transaction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.transaction-icon.income {
  background: #f0f9ff;
}

.transaction-icon.expense {
  background: #fef2f2;
}

:deep(.amount.income) {
  color: #07c160;
  font-weight: 600;
}

:deep(.amount.expense) {
  color: #ee0a24;
  font-weight: 600;
}

:deep(.amount.transfer) {
  color: #1989fa;
  font-weight: 600;
}

.transaction-icon.transfer {
  background: linear-gradient(135deg, #1989fa, #07c160);
  color: white;
}

/* 批量操作样式 */
.batch-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #ebedf0;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.batch-info {
  flex: 1;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

/* 批量模式下的页面底部间距 */
.transactions-page.batch-mode {
  padding-bottom: 80px;
}

/* 操作菜单样式 */
:deep(.van-action-sheet__item) {
  font-size: 16px;
  padding: 16px;
}

:deep(.van-action-sheet__name) {
  font-weight: 500;
}


</style>
