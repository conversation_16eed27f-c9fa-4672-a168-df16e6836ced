<template>
  <div class="transfer-success-page">
    <van-nav-bar
      title="转账成功"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="cross" @click="goHome" />
      </template>
    </van-nav-bar>

    <div class="success-content">
      <!-- 成功状态 -->
      <div class="success-status">
        <div class="success-icon">
          <van-icon name="checked" size="64" color="#07c160" />
        </div>
        <h2>转账成功</h2>
        <p class="success-message">您的转账已成功处理</p>
      </div>

      <!-- 转账详情 -->
      <van-cell-group inset title="转账详情">
        <van-cell 
          title="转账金额" 
          :value="formatCurrency(transferResult.amount, transferResult.currency)"
          class="amount-cell"
        />
        <van-cell title="转出账户" :value="transferResult.fromAccountName" />
        <van-cell title="转入账户" :value="transferResult.toAccountName" />
        <van-cell title="手续费" :value="formatCurrency(transferResult.fee, transferResult.currency)" />
        <van-cell title="交易时间" :value="formatDateTime(transferResult.completedAt)" />
        <van-cell title="交易单号" :value="transferResult.referenceNumber" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          type="primary"
          block
          size="large"
          @click="viewTransactionDetail"
        >
          <van-icon name="bill-o" />
          查看交易详情
        </van-button>
        
        <van-button
          block
          size="large"
          @click="makeAnotherTransfer"
          class="secondary-btn"
        >
          <van-icon name="exchange" />
          再次转账
        </van-button>
        
        <van-button
          block
          size="large"
          @click="goHome"
          class="secondary-btn"
        >
          <van-icon name="wap-home-o" />
          返回首页
        </van-button>
      </div>

      <!-- 分享功能 -->
      <div class="share-section">
        <van-cell-group inset title="分享">
          <van-cell 
            title="分享转账凭证" 
            is-link 
            @click="shareReceipt"
          >
            <template #icon>
              <van-icon name="share-o" />
            </template>
          </van-cell>
          <van-cell 
            title="保存到相册" 
            is-link 
            @click="saveToAlbum"
          >
            <template #icon>
              <van-icon name="photo-o" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 相关推荐 -->
      <div class="recommendation-section">
        <van-cell-group inset title="您可能还需要">
          <van-cell 
            title="设置转账模板" 
            label="保存常用转账信息，下次更便捷"
            is-link 
            @click="createTemplate"
          >
            <template #icon>
              <van-icon name="bookmark-o" />
            </template>
          </van-cell>
          <van-cell 
            title="查看账户余额" 
            label="查看最新的账户余额信息"
            is-link 
            @click="viewAccounts"
          >
            <template #icon>
              <van-icon name="balance-list-o" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 转账凭证弹窗 -->
    <van-popup 
      v-model:show="showReceiptPopup" 
      position="center" 
      round
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div class="receipt-content" ref="receiptRef">
        <div class="receipt-header">
          <h3>转账凭证</h3>
          <p>{{ formatDateTime(transferResult.completedAt) }}</p>
        </div>
        
        <div class="receipt-body">
          <div class="receipt-amount">
            <span class="currency">{{ getCurrencySymbol(transferResult.currency) }}</span>
            <span class="amount">{{ transferResult.amount }}</span>
          </div>
          
          <div class="receipt-details">
            <div class="detail-row">
              <span>转出账户</span>
              <span>{{ transferResult.fromAccountName }}</span>
            </div>
            <div class="detail-row">
              <span>转入账户</span>
              <span>{{ transferResult.toAccountName }}</span>
            </div>
            <div class="detail-row">
              <span>手续费</span>
              <span>{{ formatCurrency(transferResult.fee, transferResult.currency) }}</span>
            </div>
            <div class="detail-row">
              <span>交易单号</span>
              <span>{{ transferResult.referenceNumber }}</span>
            </div>
          </div>
        </div>
        
        <div class="receipt-footer">
          <van-button type="primary" size="small" @click="shareReceipt">
            分享
          </van-button>
          <van-button size="small" @click="showReceiptPopup = false">
            关闭
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { formatCurrency } from '@/utils/format'
import { getCurrencySymbol } from '@/config/currencies'
import { showToast, showSuccessToast } from 'vant'

const router = useRouter()
const route = useRoute()

// 响应式数据
const showReceiptPopup = ref(false)
const receiptRef = ref(null)

// 转账结果数据
const transferResult = reactive({
  amount: 0,
  currency: 'MYR',
  fee: 0,
  fromAccountName: '',
  toAccountName: '',
  referenceNumber: '',
  completedAt: '',
  transferId: ''
})

// 方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const goHome = () => {
  router.replace('/')
}

const viewTransactionDetail = () => {
  if (transferResult.transferId) {
    router.push(`/transaction/detail/${transferResult.transferId}`)
  } else {
    router.push('/transactions')
  }
}

const makeAnotherTransfer = () => {
  router.push('/transfer')
}

const shareReceipt = async () => {
  showReceiptPopup.value = true
  
  // 模拟分享功能
  setTimeout(() => {
    if (navigator.share) {
      navigator.share({
        title: '转账凭证',
        text: `转账成功：${formatCurrency(transferResult.amount, transferResult.currency)}`,
        url: window.location.href
      }).catch(console.error)
    } else {
      // 复制到剪贴板
      const text = `转账成功\n金额：${formatCurrency(transferResult.amount, transferResult.currency)}\n交易单号：${transferResult.referenceNumber}`
      navigator.clipboard.writeText(text).then(() => {
        showSuccessToast('转账信息已复制到剪贴板')
      }).catch(() => {
        showToast('分享功能暂不可用')
      })
    }
  }, 100)
}

const saveToAlbum = () => {
  // 模拟保存到相册功能
  showToast('保存功能开发中')
}

const createTemplate = () => {
  // 跳转到创建转账模板页面
  router.push('/transfer/template/add')
}

const viewAccounts = () => {
  router.push('/accounts')
}

// 生命周期
onMounted(() => {
  // 从路由参数获取转账结果数据
  if (route.query.data) {
    try {
      const data = JSON.parse(decodeURIComponent(route.query.data))
      Object.assign(transferResult, data)
    } catch (error) {
      console.error('解析转账结果数据失败:', error)
      showToast('数据错误')
      router.replace('/transactions')
    }
  } else {
    showToast('缺少转账结果数据')
    router.replace('/transactions')
  }
})
</script>

<style scoped>
.transfer-success-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.success-content {
  padding: 16px;
}

.success-status {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
}

.success-icon {
  margin-bottom: 16px;
}

.success-status h2 {
  font-size: 24px;
  font-weight: bold;
  color: #07c160;
  margin: 0 0 8px 0;
}

.success-message {
  color: #646566;
  font-size: 14px;
  margin: 0;
}

.amount-cell .van-cell__value {
  color: #07c160;
  font-weight: bold;
  font-size: 18px;
}

.action-section {
  margin: 24px 0;
}

.action-section .van-button {
  height: 50px;
  font-size: 16px;
  margin-bottom: 12px;
}

.secondary-btn {
  background: white;
  color: #1989fa;
  border-color: #1989fa;
}

.share-section,
.recommendation-section {
  margin: 16px 0;
}

.receipt-content {
  padding: 24px;
  text-align: center;
}

.receipt-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.receipt-header p {
  margin: 0 0 24px 0;
  color: #646566;
  font-size: 14px;
}

.receipt-amount {
  margin: 24px 0;
}

.receipt-amount .currency {
  font-size: 20px;
  color: #646566;
}

.receipt-amount .amount {
  font-size: 32px;
  font-weight: bold;
  color: #07c160;
  margin-left: 4px;
}

.receipt-details {
  text-align: left;
  margin: 24px 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #ebedf0;
}

.detail-row:last-child {
  border-bottom: none;
}

.receipt-footer {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
