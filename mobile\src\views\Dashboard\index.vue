<template>
  <div class="dashboard">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="仪表盘"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <div class="nav-actions">
          <van-button
            size="mini"
            type="primary"
            plain
            @click="showCurrencyPicker = true"
            class="currency-btn"
          >
            {{ selectedCurrency }}
          </van-button>
          <van-icon name="search" size="18" @click="showSearch = true" />
        </div>
      </template>
    </van-nav-bar>

    <!-- 页面内容 -->
    <div class="page-container">
      <!-- 用户欢迎信息 -->
      <div class="welcome-card">
        <div class="welcome-header">
          <div class="greeting-section">
            <h3>你好，{{ userStore.user?.display_name || 'test' }}</h3>
            <p class="date-info">{{ currentDate }}</p>
          </div>
          <div class="welcome-avatar">
            <div class="avatar-circle">
              <van-icon name="user-o" size="24" />
            </div>
            <div class="status-indicator"></div>
          </div>
        </div>

        <div class="quick-stats">
          <div class="stat-item">
            <div class="stat-content">
              <span class="stat-label">今日支出</span>
              <span class="stat-value">
                <van-loading v-if="!dataLoaded" size="16px" color="#1e40af" />
                <span v-else>{{ formatCurrency(filteredStats.todayExpense || 0, selectedCurrency) }}</span>
              </span>
            </div>
            <div class="stat-icon expense">
              💸
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-content">
              <span class="stat-label">本月预算剩余</span>
              <span class="stat-value" :class="{ 'budget-exceeded': filteredStats.monthlyBudgetRemaining < 0 }">
                <van-loading v-if="!dataLoaded" size="16px" color="#1e40af" />
                <span v-else>{{ formatCurrency(filteredStats.monthlyBudgetRemaining || 0, selectedCurrency) }}</span>
              </span>
            </div>
            <div class="stat-icon budget">
              📊
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card income">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-label">总收入</div>
            <div class="stat-value">
              <van-loading v-if="!dataLoaded" size="16px" />
              <span v-else>{{ formatCurrency(filteredStats.totalIncome, selectedCurrency) }}</span>
            </div>
            <div class="stat-change" :class="getChangeClass(filteredStats.incomeChange)">
              {{ dataLoaded ? formatChange(filteredStats.incomeChange) : '--' }}
            </div>
          </div>
        </div>

        <div class="stat-card expense">
          <div class="stat-icon">💸</div>
          <div class="stat-content">
            <div class="stat-label">总支出</div>
            <div class="stat-value">
              <van-loading v-if="!dataLoaded" size="16px" />
              <span v-else>{{ formatCurrency(filteredStats.totalExpense, selectedCurrency) }}</span>
            </div>
            <div class="stat-change" :class="getChangeClass(filteredStats.expenseChange, true)">
              {{ dataLoaded ? formatChange(filteredStats.expenseChange) : '--' }}
            </div>
          </div>
        </div>

        <div class="stat-card net-worth">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-label">净资产</div>
            <div class="stat-value">
              <van-loading v-if="!dataLoaded" size="16px" />
              <span v-else>{{ formatCurrency(filteredStats.netWorth, selectedCurrency) }}</span>
            </div>
            <div class="stat-change" :class="getChangeClass(filteredStats.netWorthChange)">
              {{ dataLoaded ? formatChange(filteredStats.netWorthChange) : '--' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h4>快速操作</h4>
        <div class="action-grid">
          <div class="action-item" @click="$router.push('/transaction/add')">
            <van-icon name="plus" size="20" />
            <span>记账</span>
          </div>
          <div class="action-item" @click="$router.push('/transfer')">
            <van-icon name="exchange" size="20" />
            <span>转账</span>
          </div>
          <div class="action-item" @click="$router.push('/account/add')">
            <van-icon name="credit-pay" size="20" />
            <span>添加账户</span>
          </div>
          <div class="action-item" @click="showStats">
            <van-icon name="chart-trending-o" size="20" />
            <span>统计</span>
          </div>
          <div class="action-item" @click="$router.push('/budgets')">
            <van-icon name="bag-o" size="20" />
            <span>预算</span>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <h4>收支趋势</h4>
            <van-button size="mini" type="primary" plain @click="showChartOptions = true">
              最近6个月
            </van-button>
          </div>
          <div class="chart-content">
            <BaseChart 
              v-if="chartData.incomeExpense"
              :options="chartData.incomeExpense" 
              height="200px" 
            />
            <div v-else class="chart-loading">
              <van-loading size="24px" />
              <span>加载中...</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近交易 -->
      <div class="recent-transactions">
        <div class="section-header">
          <h4>最近交易</h4>
          <van-button 
            size="mini" 
            type="primary" 
            plain
            @click="$router.push('/transactions')"
          >
            查看全部
          </van-button>
        </div>
        
        <div v-if="loading" class="loading-container">
          <van-loading size="24px" />
          <span>加载中...</span>
        </div>
        
        <div v-else-if="recentTransactions.length === 0" class="empty-container">
          <div class="empty-icon">📝</div>
          <p>暂无交易记录</p>
          <van-button 
            type="primary" 
            size="small"
            @click="$router.push('/transaction/add')"
          >
            立即记账
          </van-button>
        </div>
        
        <van-cell-group v-else inset>
          <van-cell
            v-for="transaction in recentTransactions"
            :key="transaction.id"
            :title="transaction.description"
            :label="transaction.date"
            :value="formatCurrency(transaction.amount, transaction.currency)"
            :value-class="transaction.type === 'income' ? 'amount income' : 'amount expense'"
            is-link
            @click="viewTransaction(transaction)"
          >
            <template #icon>
              <div class="transaction-icon" :class="transaction.type">
                {{ transaction.type === 'income' ? '💰' : '💸' }}
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>

    <!-- 搜索弹窗 -->
    <van-popup v-model:show="showSearch" position="top" :style="{ height: '100%' }">
      <div class="search-container">
        <van-nav-bar title="搜索交易" left-arrow @click-left="showSearch = false" />
        <!-- 搜索功能待实现 -->
      </div>
    </van-popup>

    <!-- 货币选择弹窗 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom" round>
      <div class="currency-picker">
        <div class="picker-header">
          <h3>选择货币</h3>
          <van-button size="mini" type="primary" @click="showCurrencyPicker = false">
            确定
          </van-button>
        </div>
        <van-cell-group>
          <van-cell
            v-for="currency in availableCurrencies"
            :key="currency.code"
            :title="currency.name"
            :label="currency.code"
            :value="currency.symbol"
            clickable
            @click="selectCurrency(currency.code)"
          >
            <template #right-icon>
              <van-icon
                v-if="selectedCurrency === currency.code"
                name="success"
                color="#1989fa"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useTransactionsStore } from '@/stores/transactions'
import { useBudgetsStore } from '@/stores/budgets'
import BaseChart from '@/components/charts/BaseChart.vue'
import { formatCurrency } from '@/utils/format'
import { getCurrencyOptionsForAccounts, DEFAULT_CURRENCY } from '@/config/currencies'

const router = useRouter()
const userStore = useAuthStore()
const transactionsStore = useTransactionsStore()
const budgetsStore = useBudgetsStore()

// 响应式数据
const loading = ref(false)
const dataLoaded = ref(false)
const showSearch = ref(false)
const showChartOptions = ref(false)
const showCurrencyPicker = ref(false)
const selectedCurrency = ref(localStorage.getItem('dashboard-currency') || DEFAULT_CURRENCY)

// 可用货币列表 - 使用统一配置
const availableCurrencies = ref(getCurrencyOptionsForAccounts())

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }
  return now.toLocaleDateString('zh-CN', options)
})

// 按货币过滤的统计数据
const filteredStats = computed(() => {
  // 如果数据还没有完全加载，返回默认值
  if (!dataLoaded.value) {
    return {
      todayExpense: 0,
      monthlyBudgetRemaining: 0,
      totalIncome: 0,
      totalExpense: 0,
      netWorth: 0,
      incomeChange: 0,
      expenseChange: 0,
      netWorthChange: 0
    }
  }
  return calculateCurrencyStats(selectedCurrency.value)
})

// 最近交易
const recentTransactions = computed(() => transactionsStore.recentTransactions)

// 图表数据
const chartData = reactive({
  incomeExpense: null
})

// 初始化数据
onMounted(async () => {
  await loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  dataLoaded.value = false
  try {
    // 并行加载交易和预算数据
    await Promise.all([
      transactionsStore.fetchTransactions(),
      budgetsStore.fetchBudgets()
    ])
    generateChartData()
    dataLoaded.value = true
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生成图表数据
const generateChartData = () => {
  console.log('仪表板: 开始生成图表数据...')

  // 获取最近6个月的数据
  const monthlyData = getMonthlyData()
  console.log('仪表板: 月度数据:', monthlyData)

  chartData.incomeExpense = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = params[0].name + '<br/>'
        params.forEach(param => {
          result += `${param.seriesName}: RM ${param.value.toFixed(2)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['收入', '支出'],
      bottom: 0
    },
    grid: {
      top: 20,
      left: 40,
      right: 20,
      bottom: 40
    },
    xAxis: {
      type: 'category',
      data: monthlyData.months,
      axisLabel: {
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12,
        formatter: 'RM {value}'
      }
    },
    series: [
      {
        name: '收入',
        type: 'line',
        data: monthlyData.income,
        itemStyle: { color: '#07c160' },
        lineStyle: { color: '#07c160' },
        smooth: true
      },
      {
        name: '支出',
        type: 'line',
        data: monthlyData.expense,
        itemStyle: { color: '#ee0a24' },
        lineStyle: { color: '#ee0a24' },
        smooth: true
      }
    ]
  }
}

// 获取月度数据
const getMonthlyData = () => {
  const transactions = transactionsStore.transactions
  const now = new Date()
  const monthlyStats = {}

  // 初始化最近6个月
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    const label = `${date.getMonth() + 1}月`

    monthlyStats[key] = {
      label,
      income: 0,
      expense: 0
    }
  }

  // 统计交易数据
  transactions.forEach(transaction => {
    const date = new Date(transaction.date)
    const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`

    if (monthlyStats[key]) {
      if (transaction.type === 'income') {
        monthlyStats[key].income += transaction.amount
      } else if (transaction.type === 'expense') {
        monthlyStats[key].expense += transaction.amount
      }
    }
  })

  const months = Object.values(monthlyStats).map(stat => stat.label)
  const income = Object.values(monthlyStats).map(stat => stat.income)
  const expense = Object.values(monthlyStats).map(stat => stat.expense)

  return { months, income, expense }
}

// 查看交易详情
const viewTransaction = (transaction) => {
  router.push(`/transaction/edit/${transaction.id}`)
}

// 显示统计
const showStats = () => {
  router.push('/statistics')
}

// 格式化变化百分比
const formatChange = (change) => {
  if (change === 0) return '0%'
  const sign = change > 0 ? '+' : ''
  return `${sign}${change}%`
}

// 获取变化样式类
const getChangeClass = (change, isExpense = false) => {
  if (change === 0) return 'neutral'

  // 对于支出，增加是负面的，减少是正面的
  if (isExpense) {
    return change > 0 ? 'negative' : 'positive'
  }

  // 对于收入和净资产，增加是正面的，减少是正面的
  return change > 0 ? 'positive' : 'negative'
}

// 选择货币
const selectCurrency = (currency) => {
  selectedCurrency.value = currency
  localStorage.setItem('dashboard-currency', currency)
  showCurrencyPicker.value = false

  // 货币切换时，如果数据已加载，立即重新计算
  // 这样可以避免显示错误的中间状态
}

// 按货币计算统计数据
const calculateCurrencyStats = (currency) => {
  const transactions = transactionsStore.transactions.filter(t => t.currency === currency)
  const now = new Date()
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  // 今日的数据
  const todayTransactions = transactions.filter(t => {
    const date = new Date(t.date)
    const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    return transactionDate.getTime() === today.getTime()
  })

  // 当前月份的数据
  const currentMonthTransactions = transactions.filter(t => {
    const date = new Date(t.date)
    return date.getMonth() === currentMonth && date.getFullYear() === currentYear
  })

  // 上个月的数据
  const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1
  const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear
  const lastMonthTransactions = transactions.filter(t => {
    const date = new Date(t.date)
    return date.getMonth() === lastMonth && date.getFullYear() === lastMonthYear
  })

  // 计算今日支出
  const todayExpense = todayTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0)

  // 计算总数据
  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0)

  const totalExpense = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0)

  // 计算当前月份数据
  const currentMonthIncome = currentMonthTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0)

  const currentMonthExpense = currentMonthTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0)

  // 计算上个月数据
  const lastMonthIncome = lastMonthTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0)

  const lastMonthExpense = lastMonthTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0)

  // 计算变化百分比
  const incomeChange = calculatePercentageChange(lastMonthIncome, currentMonthIncome)
  const expenseChange = calculatePercentageChange(lastMonthExpense, currentMonthExpense)
  const netWorthChange = calculatePercentageChange(
    lastMonthIncome - lastMonthExpense,
    currentMonthIncome - currentMonthExpense
  )

  // 计算本月预算剩余
  const monthlyBudgetRemaining = calculateMonthlyBudgetRemaining(currency, currentMonthExpense)

  return {
    todayExpense,
    monthlyBudgetRemaining,
    totalIncome,
    totalExpense,
    netWorth: totalIncome - totalExpense,
    incomeChange,
    expenseChange,
    netWorthChange
  }
}

// 计算百分比变化
const calculatePercentageChange = (oldValue, newValue) => {
  if (oldValue === 0) {
    return newValue > 0 ? 100 : 0
  }
  return Math.round(((newValue - oldValue) / oldValue) * 100)
}

// 计算本月预算剩余
const calculateMonthlyBudgetRemaining = (currency, currentMonthExpense) => {
  try {
    // 获取当前月份信息
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = now.getMonth() + 1 // 1-12
    const currentDateStr = `${currentYear}-${String(currentMonth).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`

    // 计算当前月份指定货币的预算总额
    let monthlyBudgetTotal = 0

    if (budgetsStore.budgets && budgetsStore.budgets.length > 0) {
      budgetsStore.budgets.forEach((budget) => {
        // 只计算指定货币的预算
        if (budget.currency === currency) {
          // 检查预算期间是否包含当前日期
          if (budget.start_date <= currentDateStr && budget.end_date >= currentDateStr) {
            monthlyBudgetTotal += budget.amount || 0
          }
        }
      })
    }

    // 计算剩余预算 (允许负数表示超支)
    const remaining = monthlyBudgetTotal - currentMonthExpense
    return remaining
  } catch (error) {
    console.error('计算本月预算剩余失败:', error)
    return 0
  }
}
</script>

<style scoped>
.dashboard {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  min-height: 100vh;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.currency-btn {
  min-width: 50px;
  height: 28px;
  font-size: 12px;
  font-weight: 600;
}

.welcome-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  padding: 24px;
  margin: 16px;
  border-radius: 24px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(30, 58, 138, 0.15),
    0 8px 16px rgba(30, 58, 138, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.08) 0%, transparent 70%);
  border-radius: 50%;
}

.welcome-card::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(55, 48, 163, 0.06) 0%, transparent 70%);
  border-radius: 50%;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.greeting-section h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.3px;
  color: #1e40af;
}

.date-info {
  margin: 0;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.welcome-card .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  padding: 16px 18px;
  border-radius: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.welcome-card .stat-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.welcome-card .stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.welcome-card .stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  letter-spacing: -0.3px;
}

.welcome-card .stat-value.budget-exceeded {
  color: #dc2626;
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.welcome-avatar {
  position: relative;
}

.avatar-circle {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
}

.avatar-circle .van-icon {
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #10b981, #059669);
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 0 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-card.net-worth {
  grid-column: 1 / -1;
}

.stat-icon {
  font-size: 24px;
  margin-right: 12px;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 2px;
}

.stat-change {
  font-size: 12px;
}

.stat-change.positive {
  color: #07c160;
}

.stat-change.negative {
  color: #ee0a24;
}

.stat-change.neutral {
  color: #969799;
}

.quick-actions {
  padding: 0 16px;
  margin-bottom: 24px;
}

.quick-actions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.action-item {
  background: white;
  border-radius: 12px;
  padding: 16px 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: transform 0.2s;
}

.action-item:active {
  transform: scale(0.95);
}

.action-item span {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #646566;
}

.chart-section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.currency-picker {
  padding: 20px 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
  border-bottom: 1px solid #ebedf0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.chart-content {
  height: 200px;
}

.chart-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #969799;
  gap: 8px;
}

.recent-transactions {
  padding: 0 16px;
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  color: #323233;
}

.transaction-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.transaction-icon.income {
  background: #f0f9ff;
}

.transaction-icon.expense {
  background: #fef2f2;
}

:deep(.amount.income) {
  color: #07c160;
  font-weight: 600;
}

:deep(.amount.expense) {
  color: #ee0a24;
  font-weight: 600;
}

.search-container {
  height: 100%;
  background: white;
}

/* 预算超支状态样式 */
.budget-exceeded {
  color: #ee0a24 !important;
  font-weight: 600;
}
</style>
