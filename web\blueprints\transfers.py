"""
转账功能API蓝图
提供转账相关的API接口，包括创建转账、验证转账、获取转账记录等功能
"""

import datetime
import uuid
import logging
from flask import Blueprint, request, jsonify
from auth import login_required, get_current_user_id
from database import get_db_connection

# 创建蓝图
bp = Blueprint('transfers', __name__, url_prefix='/api/transfers')

# 配置日志
logger = logging.getLogger(__name__)

def get_transfer_type(from_account_type, to_account_type):
    """根据账户类型确定转账类型"""
    type_mapping = {
        ('bank', 'ewallet'): 'bank_to_ewallet',
        ('bank', 'bank'): 'bank_to_bank', 
        ('ewallet', 'ewallet'): 'ewallet_to_ewallet',
        ('ewallet', 'bank'): 'ewallet_to_bank'
    }
    return type_mapping.get((from_account_type, to_account_type))

def calculate_transfer_fee(amount, transfer_type):
    """计算转账手续费"""
    conn = get_db_connection()
    
    # 获取转账配置
    config = conn.execute('''
        SELECT fee_type, fee_amount, fee_rate, fee_min, fee_max
        FROM transfer_config 
        WHERE transfer_type = ? AND is_active = 1
    ''', (transfer_type,)).fetchone()
    
    if not config:
        return 0.0
    
    fee_type = config['fee_type']
    
    if fee_type == 'fixed':
        return float(config['fee_amount'] or 0)
    elif fee_type == 'percentage':
        fee = amount * float(config['fee_rate'] or 0)
        fee_min = float(config['fee_min'] or 0)
        fee_max = float(config['fee_max'] or float('inf'))
        return max(fee_min, min(fee, fee_max))
    
    return 0.0

def validate_transfer_limits(user_id, transfer_type, amount):
    """验证转账限额"""
    conn = get_db_connection()
    
    # 获取转账配置
    config = conn.execute('''
        SELECT limit_min, limit_max, daily_limit, monthly_limit
        FROM transfer_config 
        WHERE transfer_type = ? AND is_active = 1
    ''', (transfer_type,)).fetchone()
    
    if not config:
        return False, "转账类型配置不存在"
    
    # 检查单笔限额
    if amount < config['limit_min']:
        return False, f"转账金额不能少于 {config['limit_min']}"
    
    if amount > config['limit_max']:
        return False, f"转账金额不能超过 {config['limit_max']}"
    
    # 检查日限额
    today = datetime.date.today().isoformat()
    daily_usage = conn.execute('''
        SELECT COALESCE(daily_amount, 0) as daily_amount
        FROM transfer_limits_usage
        WHERE user_id = ? AND transfer_type = ? AND date = ?
    ''', (user_id, transfer_type, today)).fetchone()
    
    current_daily = float(daily_usage['daily_amount'] if daily_usage else 0)
    if config['daily_limit'] and (current_daily + amount) > config['daily_limit']:
        remaining = config['daily_limit'] - current_daily
        return False, f"超出日转账限额，今日剩余额度: {remaining}"
    
    # 检查月限额
    month_start = datetime.date.today().replace(day=1).isoformat()
    monthly_usage = conn.execute('''
        SELECT COALESCE(SUM(daily_amount), 0) as monthly_amount
        FROM transfer_limits_usage
        WHERE user_id = ? AND transfer_type = ? AND date >= ?
    ''', (user_id, transfer_type, month_start)).fetchone()
    
    current_monthly = float(monthly_usage['monthly_amount'] if monthly_usage else 0)
    if config['monthly_limit'] and (current_monthly + amount) > config['monthly_limit']:
        remaining = config['monthly_limit'] - current_monthly
        return False, f"超出月转账限额，本月剩余额度: {remaining}"
    
    return True, "验证通过"

def update_transfer_limits_usage(user_id, transfer_type, amount):
    """更新转账限额使用记录"""
    conn = get_db_connection()
    today = datetime.date.today().isoformat()
    now = datetime.datetime.now().isoformat()
    
    # 检查今日记录是否存在
    existing = conn.execute('''
        SELECT id FROM transfer_limits_usage
        WHERE user_id = ? AND transfer_type = ? AND date = ?
    ''', (user_id, transfer_type, today)).fetchone()
    
    if existing:
        # 更新现有记录
        conn.execute('''
            UPDATE transfer_limits_usage
            SET daily_amount = daily_amount + ?, 
                daily_count = daily_count + 1,
                updated_at = ?
            WHERE user_id = ? AND transfer_type = ? AND date = ?
        ''', (amount, now, user_id, transfer_type, today))
    else:
        # 创建新记录
        record_id = str(uuid.uuid4())
        conn.execute('''
            INSERT INTO transfer_limits_usage (
                id, user_id, transfer_type, date, daily_amount, daily_count,
                monthly_amount, monthly_count, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, 1, ?, 1, ?, ?)
        ''', (record_id, user_id, transfer_type, today, amount, amount, now, now))
    
    conn.commit()

@bp.route('/config', methods=['GET'])
@login_required
def get_transfer_config():
    """获取转账配置"""
    try:
        conn = get_db_connection()
        
        configs = conn.execute('''
            SELECT transfer_type, fee_type, fee_amount, fee_rate, fee_min, fee_max,
                   limit_min, limit_max, daily_limit, monthly_limit
            FROM transfer_config
            WHERE is_active = 1
            ORDER BY transfer_type
        ''').fetchall()
        
        config_data = {}
        for config in configs:
            config_data[config['transfer_type']] = {
                'fee_type': config['fee_type'],
                'fee_amount': config['fee_amount'],
                'fee_rate': config['fee_rate'],
                'fee_min': config['fee_min'],
                'fee_max': config['fee_max'],
                'limit_min': config['limit_min'],
                'limit_max': config['limit_max'],
                'daily_limit': config['daily_limit'],
                'monthly_limit': config['monthly_limit']
            }
        
        return jsonify({
            'success': True,
            'data': {
                'transfer_types': config_data
            }
        })
        
    except Exception as e:
        logger.error(f"获取转账配置失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取转账配置失败'}), 500

@bp.route('/validate', methods=['POST'])
@login_required
def validate_transfer():
    """验证转账信息"""
    try:
        data = request.json
        user_id = get_current_user_id()
        
        # 验证必填字段
        required_fields = ['from_account_id', 'to_account_id', 'amount']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400
        
        from_account_id = data['from_account_id']
        to_account_id = data['to_account_id']
        amount = float(data['amount'])
        
        conn = get_db_connection()
        
        # 验证账户存在且属于当前用户
        from_account = conn.execute('''
            SELECT id, name, type, currency, initial_balance
            FROM accounts WHERE id = ? AND user_id = ?
        ''', (from_account_id, user_id)).fetchone()
        
        to_account = conn.execute('''
            SELECT id, name, type, currency, initial_balance  
            FROM accounts WHERE id = ? AND user_id = ?
        ''', (to_account_id, user_id)).fetchone()
        
        if not from_account:
            return jsonify({'success': False, 'error': '转出账户不存在'}), 400
            
        if not to_account:
            return jsonify({'success': False, 'error': '转入账户不存在'}), 400
        
        # 验证账户不能相同
        if from_account_id == to_account_id:
            return jsonify({'success': False, 'error': '转出账户和转入账户不能相同'}), 400
        
        # 验证货币类型一致
        if from_account['currency'] != to_account['currency']:
            return jsonify({'success': False, 'error': '转出账户和转入账户货币类型必须一致'}), 400
        
        # 确定转账类型
        transfer_type = get_transfer_type(from_account['type'], to_account['type'])
        if not transfer_type:
            return jsonify({'success': False, 'error': '不支持的转账类型'}), 400
        
        # 计算手续费
        fee = calculate_transfer_fee(amount, transfer_type)
        total_amount = amount + fee
        
        # 验证账户余额
        # 计算转出账户当前余额
        income = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'income'
        ''', (from_account_id, user_id)).fetchone()[0]
        
        expense = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'expense'
        ''', (from_account_id, user_id)).fetchone()[0]
        
        current_balance = from_account['initial_balance'] + income - expense
        
        if current_balance < total_amount:
            return jsonify({
                'success': False, 
                'error': f'账户余额不足，当前余额: {current_balance}，需要: {total_amount}'
            }), 400
        
        # 验证转账限额
        is_valid, message = validate_transfer_limits(user_id, transfer_type, amount)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400
        
        # 计算剩余限额
        today = datetime.date.today().isoformat()
        daily_usage = conn.execute('''
            SELECT COALESCE(daily_amount, 0) as daily_amount
            FROM transfer_limits_usage
            WHERE user_id = ? AND transfer_type = ? AND date = ?
        ''', (user_id, transfer_type, today)).fetchone()
        
        config = conn.execute('''
            SELECT daily_limit FROM transfer_config 
            WHERE transfer_type = ? AND is_active = 1
        ''', (transfer_type,)).fetchone()
        
        current_daily = float(daily_usage['daily_amount'] if daily_usage else 0)
        daily_limit = float(config['daily_limit'] if config else 0)
        remaining_daily_limit = daily_limit - current_daily if daily_limit > 0 else None
        
        return jsonify({
            'success': True,
            'data': {
                'is_valid': True,
                'fee': fee,
                'total_amount': total_amount,
                'remaining_balance': current_balance - total_amount,
                'remaining_daily_limit': remaining_daily_limit,
                'transfer_type': transfer_type,
                'warnings': [],
                'errors': []
            }
        })
        
    except ValueError as e:
        return jsonify({'success': False, 'error': '金额格式不正确'}), 400
    except Exception as e:
        logger.error(f"验证转账信息失败: {str(e)}")
        return jsonify({'success': False, 'error': '验证转账信息失败'}), 500

@bp.route('/', methods=['POST'])
@login_required
def create_transfer():
    """创建转账"""
    try:
        data = request.json
        user_id = get_current_user_id()

        # 验证必填字段
        required_fields = ['from_account_id', 'to_account_id', 'amount', 'currency']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400

        from_account_id = data['from_account_id']
        to_account_id = data['to_account_id']
        amount = float(data['amount'])
        currency = data['currency']
        description = data.get('description', '')

        conn = get_db_connection()

        # 再次验证转账信息（安全考虑）
        from_account = conn.execute('''
            SELECT id, name, type, currency, initial_balance
            FROM accounts WHERE id = ? AND user_id = ?
        ''', (from_account_id, user_id)).fetchone()

        to_account = conn.execute('''
            SELECT id, name, type, currency, initial_balance
            FROM accounts WHERE id = ? AND user_id = ?
        ''', (to_account_id, user_id)).fetchone()

        if not from_account or not to_account:
            return jsonify({'success': False, 'error': '账户不存在'}), 400

        if from_account['currency'] != currency or to_account['currency'] != currency:
            return jsonify({'success': False, 'error': '货币类型不匹配'}), 400

        # 确定转账类型和计算手续费
        transfer_type = get_transfer_type(from_account['type'], to_account['type'])
        fee = calculate_transfer_fee(amount, transfer_type)
        total_amount = amount + fee

        # 验证余额和限额
        income = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'income'
        ''', (from_account_id, user_id)).fetchone()[0]

        expense = conn.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM transactions
            WHERE account = ? AND user_id = ? AND type = 'expense'
        ''', (from_account_id, user_id)).fetchone()[0]

        current_balance = from_account['initial_balance'] + income - expense

        if current_balance < total_amount:
            return jsonify({'success': False, 'error': '账户余额不足'}), 400

        is_valid, message = validate_transfer_limits(user_id, transfer_type, amount)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400

        # 生成转账记录
        transfer_id = str(uuid.uuid4())
        reference_number = f"TXN{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}{transfer_id[:6].upper()}"
        now = datetime.datetime.now().isoformat()

        # 开始数据库事务
        try:
            # 创建转账记录
            conn.execute('''
                INSERT INTO transfers (
                    id, user_id, from_account_id, to_account_id, amount, fee, currency,
                    description, status, transfer_type, reference_number, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?)
            ''', (transfer_id, user_id, from_account_id, to_account_id, amount, fee,
                  currency, description, transfer_type, reference_number, now))

            # 创建转出交易记录
            from_transaction_id = str(uuid.uuid4())
            conn.execute('''
                INSERT INTO transactions (
                    id, user_id, date, type, category, description, account, amount, currency,
                    attachment, created_at, updated_at
                ) VALUES (?, ?, ?, 'expense', '转账', ?, ?, ?, ?, '', ?, ?)
            ''', (from_transaction_id, user_id, now[:10], f"转账至{to_account['name']} - {description}",
                  from_account_id, total_amount, currency, now, now))

            # 创建转入交易记录
            to_transaction_id = str(uuid.uuid4())
            conn.execute('''
                INSERT INTO transactions (
                    id, user_id, date, type, category, description, account, amount, currency,
                    attachment, created_at, updated_at
                ) VALUES (?, ?, ?, 'income', '转账', ?, ?, ?, ?, '', ?, ?)
            ''', (to_transaction_id, user_id, now[:10], f"来自{from_account['name']}的转账 - {description}",
                  to_account_id, amount, currency, now, now))

            # 更新转账状态为完成
            completed_at = datetime.datetime.now().isoformat()
            conn.execute('''
                UPDATE transfers SET status = 'completed', completed_at = ?
                WHERE id = ?
            ''', (completed_at, transfer_id))

            # 更新转账限额使用记录
            update_transfer_limits_usage(user_id, transfer_type, amount)

            conn.commit()

            logger.info(f"转账创建成功: {transfer_id}, 用户: {user_id}")

            return jsonify({
                'success': True,
                'data': {
                    'transfer_id': transfer_id,
                    'reference_number': reference_number,
                    'fee': fee,
                    'total_amount': total_amount,
                    'status': 'completed',
                    'completed_at': completed_at
                }
            })

        except Exception as e:
            conn.rollback()
            logger.error(f"创建转账事务失败: {str(e)}")
            return jsonify({'success': False, 'error': '转账处理失败'}), 500

    except ValueError as e:
        return jsonify({'success': False, 'error': '金额格式不正确'}), 400
    except Exception as e:
        logger.error(f"创建转账失败: {str(e)}")
        return jsonify({'success': False, 'error': '创建转账失败'}), 500

@bp.route('/', methods=['GET'])
@login_required
def get_transfers():
    """获取转账记录"""
    try:
        user_id = get_current_user_id()

        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status = request.args.get('status')
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        account_id = request.args.get('account_id')

        # 构建查询条件
        where_conditions = ['user_id = ?']
        params = [user_id]

        if status:
            where_conditions.append('status = ?')
            params.append(status)

        if from_date:
            where_conditions.append('created_at >= ?')
            params.append(from_date)

        if to_date:
            where_conditions.append('created_at <= ?')
            params.append(to_date + ' 23:59:59')

        if account_id:
            where_conditions.append('(from_account_id = ? OR to_account_id = ?)')
            params.extend([account_id, account_id])

        where_clause = ' AND '.join(where_conditions)

        conn = get_db_connection()

        # 获取总数
        count_query = f'SELECT COUNT(*) FROM transfers WHERE {where_clause}'
        total = conn.execute(count_query, params).fetchone()[0]

        # 获取转账记录
        offset = (page - 1) * limit
        query = f'''
            SELECT t.*,
                   fa.name as from_account_name, fa.type as from_account_type,
                   ta.name as to_account_name, ta.type as to_account_type
            FROM transfers t
            LEFT JOIN accounts fa ON t.from_account_id = fa.id
            LEFT JOIN accounts ta ON t.to_account_id = ta.id
            WHERE {where_clause}
            ORDER BY t.created_at DESC
            LIMIT ? OFFSET ?
        '''

        transfers = conn.execute(query, params + [limit, offset]).fetchall()

        # 格式化转账记录
        transfer_list = []
        for transfer in transfers:
            transfer_list.append({
                'id': transfer['id'],
                'from_account': {
                    'id': transfer['from_account_id'],
                    'name': transfer['from_account_name'],
                    'type': transfer['from_account_type']
                },
                'to_account': {
                    'id': transfer['to_account_id'],
                    'name': transfer['to_account_name'],
                    'type': transfer['to_account_type']
                },
                'amount': transfer['amount'],
                'fee': transfer['fee'],
                'currency': transfer['currency'],
                'description': transfer['description'],
                'status': transfer['status'],
                'transfer_type': transfer['transfer_type'],
                'reference_number': transfer['reference_number'],
                'created_at': transfer['created_at'],
                'completed_at': transfer['completed_at'],
                'failed_at': transfer['failed_at'],
                'failure_reason': transfer['failure_reason']
            })

        # 计算分页信息
        pages = (total + limit - 1) // limit

        return jsonify({
            'success': True,
            'data': {
                'transfers': transfer_list,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': pages
                }
            }
        })

    except Exception as e:
        logger.error(f"获取转账记录失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取转账记录失败'}), 500

@bp.route('/<transfer_id>', methods=['GET'])
@login_required
def get_transfer_detail(transfer_id):
    """获取转账详情"""
    try:
        user_id = get_current_user_id()

        conn = get_db_connection()

        # 获取转账详情
        transfer = conn.execute('''
            SELECT t.*,
                   fa.name as from_account_name, fa.type as from_account_type, fa.currency as from_account_currency,
                   ta.name as to_account_name, ta.type as to_account_type, ta.currency as to_account_currency
            FROM transfers t
            LEFT JOIN accounts fa ON t.from_account_id = fa.id
            LEFT JOIN accounts ta ON t.to_account_id = ta.id
            WHERE t.id = ? AND t.user_id = ?
        ''', (transfer_id, user_id)).fetchone()

        if not transfer:
            return jsonify({'success': False, 'error': '转账记录不存在'}), 404

        return jsonify({
            'success': True,
            'data': {
                'id': transfer['id'],
                'from_account': {
                    'id': transfer['from_account_id'],
                    'name': transfer['from_account_name'],
                    'type': transfer['from_account_type'],
                    'currency': transfer['from_account_currency']
                },
                'to_account': {
                    'id': transfer['to_account_id'],
                    'name': transfer['to_account_name'],
                    'type': transfer['to_account_type'],
                    'currency': transfer['to_account_currency']
                },
                'amount': transfer['amount'],
                'fee': transfer['fee'],
                'currency': transfer['currency'],
                'description': transfer['description'],
                'status': transfer['status'],
                'transfer_type': transfer['transfer_type'],
                'reference_number': transfer['reference_number'],
                'created_at': transfer['created_at'],
                'completed_at': transfer['completed_at'],
                'failed_at': transfer['failed_at'],
                'failure_reason': transfer['failure_reason']
            }
        })

    except Exception as e:
        logger.error(f"获取转账详情失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取转账详情失败'}), 500

@bp.route('/templates', methods=['GET'])
@login_required
def get_transfer_templates():
    """获取转账模板"""
    try:
        user_id = get_current_user_id()

        conn = get_db_connection()

        # 获取转账模板
        templates = conn.execute('''
            SELECT t.*,
                   fa.name as from_account_name, fa.type as from_account_type, fa.currency as from_account_currency,
                   ta.name as to_account_name, ta.type as to_account_type, ta.currency as to_account_currency
            FROM transfer_templates t
            LEFT JOIN accounts fa ON t.from_account_id = fa.id
            LEFT JOIN accounts ta ON t.to_account_id = ta.id
            WHERE t.user_id = ?
            ORDER BY t.is_favorite DESC, t.usage_count DESC, t.created_at DESC
        ''', (user_id,)).fetchall()

        # 格式化模板数据
        template_list = []
        for template in templates:
            template_list.append({
                'id': template['id'],
                'template_name': template['template_name'],
                'from_account': {
                    'id': template['from_account_id'],
                    'name': template['from_account_name'],
                    'type': template['from_account_type'],
                    'currency': template['from_account_currency']
                },
                'to_account': {
                    'id': template['to_account_id'],
                    'name': template['to_account_name'],
                    'type': template['to_account_type'],
                    'currency': template['to_account_currency']
                },
                'amount': template['amount'],
                'description': template['description'],
                'is_favorite': bool(template['is_favorite']),
                'usage_count': template['usage_count'],
                'last_used_at': template['last_used_at'],
                'created_at': template['created_at']
            })

        return jsonify({
            'success': True,
            'data': {
                'templates': template_list
            }
        })

    except Exception as e:
        logger.error(f"获取转账模板失败: {str(e)}")
        return jsonify({'success': False, 'error': '获取转账模板失败'}), 500

@bp.route('/templates', methods=['POST'])
@login_required
def create_transfer_template():
    """创建转账模板"""
    try:
        data = request.json
        user_id = get_current_user_id()

        # 验证必填字段
        required_fields = ['template_name', 'from_account_id', 'to_account_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400

        template_name = data['template_name'].strip()
        from_account_id = data['from_account_id']
        to_account_id = data['to_account_id']
        amount = data.get('amount')
        description = data.get('description', '').strip()

        conn = get_db_connection()

        # 验证账户存在且属于当前用户
        from_account = conn.execute('''
            SELECT id FROM accounts WHERE id = ? AND user_id = ?
        ''', (from_account_id, user_id)).fetchone()

        to_account = conn.execute('''
            SELECT id FROM accounts WHERE id = ? AND user_id = ?
        ''', (to_account_id, user_id)).fetchone()

        if not from_account or not to_account:
            return jsonify({'success': False, 'error': '账户不存在'}), 400

        # 检查模板名称是否重复
        existing = conn.execute('''
            SELECT id FROM transfer_templates
            WHERE user_id = ? AND template_name = ?
        ''', (user_id, template_name)).fetchone()

        if existing:
            return jsonify({'success': False, 'error': '模板名称已存在'}), 400

        # 创建模板
        template_id = str(uuid.uuid4())
        now = datetime.datetime.now().isoformat()

        conn.execute('''
            INSERT INTO transfer_templates (
                id, user_id, template_name, from_account_id, to_account_id,
                amount, description, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (template_id, user_id, template_name, from_account_id, to_account_id,
              amount, description, now, now))

        conn.commit()

        logger.info(f"转账模板创建成功: {template_id}, 用户: {user_id}")

        return jsonify({
            'success': True,
            'data': {
                'template_id': template_id
            }
        })

    except Exception as e:
        logger.error(f"创建转账模板失败: {str(e)}")
        return jsonify({'success': False, 'error': '创建转账模板失败'}), 500

@bp.route('/templates/<template_id>', methods=['PUT'])
@login_required
def update_transfer_template(template_id):
    """更新转账模板"""
    try:
        data = request.json
        user_id = get_current_user_id()

        conn = get_db_connection()

        # 检查模板是否存在且属于当前用户
        template = conn.execute('''
            SELECT id FROM transfer_templates WHERE id = ? AND user_id = ?
        ''', (template_id, user_id)).fetchone()

        if not template:
            return jsonify({'success': False, 'error': '模板不存在'}), 404

        # 更新模板
        now = datetime.datetime.now().isoformat()
        update_fields = []
        params = []

        if 'template_name' in data:
            update_fields.append('template_name = ?')
            params.append(data['template_name'].strip())

        if 'amount' in data:
            update_fields.append('amount = ?')
            params.append(data['amount'])

        if 'description' in data:
            update_fields.append('description = ?')
            params.append(data['description'].strip())

        if 'is_favorite' in data:
            update_fields.append('is_favorite = ?')
            params.append(1 if data['is_favorite'] else 0)

        if update_fields:
            update_fields.append('updated_at = ?')
            params.extend([now, template_id, user_id])

            query = f'''
                UPDATE transfer_templates
                SET {', '.join(update_fields)}
                WHERE id = ? AND user_id = ?
            '''

            conn.execute(query, params)
            conn.commit()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"更新转账模板失败: {str(e)}")
        return jsonify({'success': False, 'error': '更新转账模板失败'}), 500

@bp.route('/templates/<template_id>', methods=['DELETE'])
@login_required
def delete_transfer_template(template_id):
    """删除转账模板"""
    try:
        user_id = get_current_user_id()

        conn = get_db_connection()

        # 检查模板是否存在且属于当前用户
        template = conn.execute('''
            SELECT id FROM transfer_templates WHERE id = ? AND user_id = ?
        ''', (template_id, user_id)).fetchone()

        if not template:
            return jsonify({'success': False, 'error': '模板不存在'}), 404

        # 删除模板
        conn.execute('''
            DELETE FROM transfer_templates WHERE id = ? AND user_id = ?
        ''', (template_id, user_id))

        conn.commit()

        logger.info(f"转账模板删除成功: {template_id}, 用户: {user_id}")

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"删除转账模板失败: {str(e)}")
        return jsonify({'success': False, 'error': '删除转账模板失败'}), 500

@bp.route('/templates/<template_id>/use', methods=['POST'])
@login_required
def use_transfer_template(template_id):
    """使用转账模板（更新使用次数和最后使用时间）"""
    try:
        user_id = get_current_user_id()

        conn = get_db_connection()

        # 检查模板是否存在且属于当前用户
        template = conn.execute('''
            SELECT id FROM transfer_templates WHERE id = ? AND user_id = ?
        ''', (template_id, user_id)).fetchone()

        if not template:
            return jsonify({'success': False, 'error': '模板不存在'}), 404

        # 更新使用统计
        now = datetime.datetime.now().isoformat()
        conn.execute('''
            UPDATE transfer_templates
            SET usage_count = usage_count + 1, last_used_at = ?, updated_at = ?
            WHERE id = ? AND user_id = ?
        ''', (now, now, template_id, user_id))

        conn.commit()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"使用转账模板失败: {str(e)}")
        return jsonify({'success': False, 'error': '使用转账模板失败'}), 500
