# 转账功能开发者指南

## 🏗️ 架构概述

转账功能采用前后端分离的架构设计，包含以下主要组件：

### 后端组件
- **API层**：`web/blueprints/transfers.py` - 转账相关的REST API
- **数据层**：转账记录、配置、模板等数据表
- **业务逻辑**：转账验证、手续费计算、限额检查等

### 前端组件
- **页面组件**：转账表单、确认页面、成功页面等
- **状态管理**：Pinia store管理转账数据
- **API接口**：前端API调用封装

## 🗄️ 数据库设计

### 核心数据表

#### 1. transfers（转账记录表）
```sql
CREATE TABLE transfers (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    from_account_id TEXT NOT NULL,
    to_account_id TEXT NOT NULL,
    amount REAL NOT NULL,
    fee REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    transfer_type TEXT NOT NULL,
    reference_number TEXT,
    created_at TEXT NOT NULL,
    completed_at TEXT,
    failed_at TEXT,
    failure_reason TEXT
);
```

#### 2. transfer_config（转账配置表）
```sql
CREATE TABLE transfer_config (
    id TEXT PRIMARY KEY,
    transfer_type TEXT NOT NULL UNIQUE,
    fee_type TEXT NOT NULL,
    fee_amount REAL,
    fee_rate REAL,
    fee_min REAL,
    fee_max REAL,
    limit_min REAL NOT NULL,
    limit_max REAL NOT NULL,
    daily_limit REAL,
    monthly_limit REAL,
    is_active BOOLEAN DEFAULT 1
);
```

#### 3. transfer_templates（转账模板表）
```sql
CREATE TABLE transfer_templates (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    template_name TEXT NOT NULL,
    from_account_id TEXT NOT NULL,
    to_account_id TEXT NOT NULL,
    amount REAL,
    description TEXT,
    is_favorite BOOLEAN DEFAULT 0,
    usage_count INTEGER DEFAULT 0,
    last_used_at TEXT
);
```

## 🔌 API接口文档

### 转账管理API

#### 获取转账配置
```http
GET /api/transfers/config
Authorization: Bearer <token>
```

#### 验证转账信息
```http
POST /api/transfers/validate
Content-Type: application/json
Authorization: Bearer <token>

{
  "from_account_id": "account_123",
  "to_account_id": "account_456",
  "amount": 100.00
}
```

#### 创建转账
```http
POST /api/transfers/
Content-Type: application/json
Authorization: Bearer <token>

{
  "from_account_id": "account_123",
  "to_account_id": "account_456",
  "amount": 100.00,
  "currency": "MYR",
  "description": "转账备注"
}
```

#### 获取转账记录
```http
GET /api/transfers/?page=1&limit=20&status=completed
Authorization: Bearer <token>
```

### 转账模板API

#### 获取转账模板
```http
GET /api/transfers/templates
Authorization: Bearer <token>
```

#### 创建转账模板
```http
POST /api/transfers/templates
Content-Type: application/json
Authorization: Bearer <token>

{
  "template_name": "给朋友转账",
  "from_account_id": "account_123",
  "to_account_id": "account_456",
  "amount": 100.00,
  "description": "朋友聚餐费用"
}
```

## 💼 业务逻辑实现

### 转账类型判断
```python
def get_transfer_type(from_account_type, to_account_type):
    """根据账户类型确定转账类型"""
    type_mapping = {
        ('bank', 'ewallet'): 'bank_to_ewallet',
        ('bank', 'bank'): 'bank_to_bank',
        ('ewallet', 'ewallet'): 'ewallet_to_ewallet',
        ('ewallet', 'bank'): 'ewallet_to_bank'
    }
    return type_mapping.get((from_account_type, to_account_type))
```

### 手续费计算
```python
def calculate_transfer_fee(amount, transfer_type):
    """计算转账手续费"""
    config = get_transfer_config(transfer_type)
    
    if config['fee_type'] == 'fixed':
        return float(config['fee_amount'] or 0)
    elif config['fee_type'] == 'percentage':
        fee = amount * float(config['fee_rate'] or 0)
        fee_min = float(config['fee_min'] or 0)
        fee_max = float(config['fee_max'] or float('inf'))
        return max(fee_min, min(fee, fee_max))
    
    return 0.0
```

### 转账限额验证
```python
def validate_transfer_limits(user_id, transfer_type, amount):
    """验证转账限额"""
    config = get_transfer_config(transfer_type)
    
    # 检查单笔限额
    if amount < config['limit_min'] or amount > config['limit_max']:
        return False, "转账金额超出限额"
    
    # 检查日限额
    daily_usage = get_daily_usage(user_id, transfer_type)
    if config['daily_limit'] and (daily_usage + amount) > config['daily_limit']:
        return False, "超出日转账限额"
    
    return True, "验证通过"
```

## 🎨 前端组件设计

### 转账页面组件结构
```vue
<template>
  <div class="transfer-page">
    <!-- 导航栏 -->
    <van-nav-bar title="转账" />
    
    <!-- 进度指示器 -->
    <van-steps :active="0">
      <van-step>填写信息</van-step>
      <van-step>确认转账</van-step>
      <van-step>转账完成</van-step>
    </van-steps>
    
    <!-- 转账表单 -->
    <van-form @submit="handleSubmit">
      <!-- 账户选择 -->
      <account-selector v-model="form.fromAccountId" />
      <account-selector v-model="form.toAccountId" />
      
      <!-- 金额输入 -->
      <amount-input v-model="form.amount" />
      
      <!-- 费用显示 -->
      <fee-display :fee-info="feeInfo" />
    </van-form>
  </div>
</template>
```

### 状态管理
```javascript
// stores/transactions.js
export const useTransactionsStore = defineStore('transactions', {
  state: () => ({
    transfers: [],
    transferTemplates: [],
    transferLoading: false
  }),
  
  actions: {
    async createTransfer(data) {
      const response = await transfersAPI.createTransfer(data)
      if (response.success) {
        await this.fetchTransfers()
        await this.fetchTransactions()
      }
      return response
    },
    
    async fetchTransferTemplates() {
      const response = await transfersAPI.getTemplates()
      if (response.success) {
        this.transferTemplates = response.data.templates
      }
      return response
    }
  }
})
```

## 🧪 测试策略

### 单元测试
- **后端测试**：使用pytest测试API接口和业务逻辑
- **前端测试**：使用Jest测试Vue组件和状态管理

### 集成测试
- **API集成测试**：测试完整的转账流程
- **前后端集成**：测试前端与后端API的交互

### 测试用例覆盖
```python
# 后端测试示例
def test_create_transfer_success(client, auth_headers, test_accounts):
    """测试创建转账成功"""
    transfer_data = {
        'from_account_id': 'account_1',
        'to_account_id': 'account_2',
        'amount': 100.0,
        'currency': 'MYR'
    }
    
    response = client.post('/api/transfers/',
                          data=json.dumps(transfer_data),
                          headers=auth_headers)
    
    assert response.status_code == 200
    assert response.json['success'] is True
```

## 🔧 配置和部署

### 环境配置
```python
# 转账功能相关配置
TRANSFER_CONFIG = {
    'DEFAULT_FEE_RATES': {
        'bank_to_ewallet': {'type': 'fixed', 'amount': 1.0},
        'bank_to_bank': {'type': 'percentage', 'rate': 0.001}
    },
    'DEFAULT_LIMITS': {
        'bank_to_ewallet': {'min': 1.0, 'max': 10000.0, 'daily': 5000.0}
    }
}
```

### 数据库迁移
```bash
# 运行转账功能相关的数据库迁移
python scripts/migrate_transfer_tables.py
```

## 🚀 性能优化

### 数据库优化
- 为转账记录表创建适当的索引
- 使用分页查询避免大量数据加载
- 定期清理过期的转账记录

### 前端优化
- 使用防抖处理金额输入验证
- 缓存转账配置信息
- 懒加载转账模板数据

### API优化
- 批量查询减少API调用次数
- 使用缓存存储转账配置
- 异步处理转账记录创建

## 🔒 安全考虑

### 数据验证
- 严格验证转账金额和账户信息
- 防止SQL注入和XSS攻击
- 验证用户权限和账户所有权

### 业务安全
- 实现转账限额控制
- 记录详细的操作日志
- 提供转账撤销机制（如需要）

### 前端安全
- 敏感数据不在前端存储
- 使用HTTPS传输数据
- 实现CSP策略防止XSS

## 📈 监控和日志

### 关键指标监控
- 转账成功率
- 转账处理时间
- 手续费收入统计
- 用户转账行为分析

### 日志记录
```python
# 转账操作日志
logger.info(f"转账创建成功: {transfer_id}, 用户: {user_id}, 金额: {amount}")
logger.error(f"转账失败: {error_message}, 用户: {user_id}")
```

## 🔄 扩展和维护

### 功能扩展点
- 支持跨货币转账
- 添加定时转账功能
- 集成外部支付网关
- 实现转账审批流程

### 维护建议
- 定期更新转账配置
- 监控转账异常情况
- 优化数据库性能
- 更新安全策略

---

*最后更新：2024年12月8日*
