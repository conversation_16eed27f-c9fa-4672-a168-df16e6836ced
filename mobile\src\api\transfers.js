import api from './index'

export const transfersAPI = {
  // 获取转账配置
  getConfig() {
    return api.get('/transfers/config')
  },

  // 验证转账信息
  validateTransfer(data) {
    return api.post('/transfers/validate', data)
  },

  // 创建转账
  createTransfer(data) {
    return api.post('/transfers/', data)
  },

  // 获取转账记录
  getTransfers(params = {}) {
    return api.get('/transfers/', { params })
  },

  // 获取转账详情
  getTransfer(id) {
    return api.get(`/transfers/${id}`)
  },

  // 获取转账模板
  getTemplates() {
    return api.get('/transfers/templates')
  },

  // 创建转账模板
  createTemplate(data) {
    return api.post('/transfers/templates', data)
  },

  // 更新转账模板
  updateTemplate(id, data) {
    return api.put(`/transfers/templates/${id}`, data)
  },

  // 删除转账模板
  deleteTemplate(id) {
    return api.delete(`/transfers/templates/${id}`)
  }
}
