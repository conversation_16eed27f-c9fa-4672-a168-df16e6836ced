"""
转账功能测试用例
测试转账API的各种功能，包括创建转账、验证转账、获取转账记录等
"""

import pytest
import json
import uuid
from datetime import datetime, timedelta
from web.app import app, init_db
from web.database import get_db_connection


@pytest.fixture
def client():
    """创建测试客户端"""
    app.config['TESTING'] = True
    app.config['DATABASE'] = ':memory:'  # 使用内存数据库
    
    with app.test_client() as client:
        with app.app_context():
            init_db()
            yield client


@pytest.fixture
def auth_headers(client):
    """创建认证头部"""
    # 创建测试用户
    user_data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    # 注册用户
    client.post('/api/auth/register', 
                data=json.dumps(user_data),
                content_type='application/json')
    
    # 登录获取token
    login_response = client.post('/api/auth/login',
                                data=json.dumps({
                                    'username': 'testuser',
                                    'password': 'testpass123'
                                }),
                                content_type='application/json')
    
    token = json.loads(login_response.data)['token']
    return {'Authorization': f'Bearer {token}'}


@pytest.fixture
def test_accounts(client, auth_headers):
    """创建测试账户"""
    accounts = []
    
    # 创建银行账户
    bank_account = {
        'name': 'Test Bank Account',
        'type': 'bank',
        'currency': 'MYR',
        'initial_balance': 1000.0
    }
    response = client.post('/api/accounts/',
                          data=json.dumps(bank_account),
                          content_type='application/json',
                          headers=auth_headers)
    accounts.append(json.loads(response.data)['data'])
    
    # 创建电子钱包账户
    ewallet_account = {
        'name': 'Test eWallet',
        'type': 'ewallet',
        'currency': 'MYR',
        'initial_balance': 500.0
    }
    response = client.post('/api/accounts/',
                          data=json.dumps(ewallet_account),
                          content_type='application/json',
                          headers=auth_headers)
    accounts.append(json.loads(response.data)['data'])
    
    return accounts


class TestTransferConfig:
    """转账配置测试"""
    
    def test_get_transfer_config(self, client, auth_headers):
        """测试获取转账配置"""
        response = client.get('/api/transfers/config', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'transfer_types' in data['data']
        
        # 检查默认配置是否存在
        transfer_types = data['data']['transfer_types']
        assert 'bank_to_ewallet' in transfer_types
        assert 'bank_to_bank' in transfer_types
        assert 'ewallet_to_ewallet' in transfer_types
        assert 'ewallet_to_bank' in transfer_types


class TestTransferValidation:
    """转账验证测试"""
    
    def test_validate_transfer_success(self, client, auth_headers, test_accounts):
        """测试转账验证成功"""
        bank_account, ewallet_account = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0
        }
        
        response = client.post('/api/transfers/validate',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['is_valid'] is True
        assert 'fee' in data['data']
        assert 'total_amount' in data['data']
    
    def test_validate_transfer_insufficient_balance(self, client, auth_headers, test_accounts):
        """测试余额不足的转账验证"""
        bank_account, ewallet_account = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 2000.0  # 超过账户余额
        }
        
        response = client.post('/api/transfers/validate',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert '余额不足' in data['error']
    
    def test_validate_transfer_same_account(self, client, auth_headers, test_accounts):
        """测试相同账户转账验证"""
        bank_account, _ = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': bank_account['id'],  # 相同账户
            'amount': 100.0
        }
        
        response = client.post('/api/transfers/validate',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert '不能相同' in data['error']


class TestTransferCreation:
    """转账创建测试"""
    
    def test_create_transfer_success(self, client, auth_headers, test_accounts):
        """测试创建转账成功"""
        bank_account, ewallet_account = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'currency': 'MYR',
            'description': '测试转账'
        }
        
        response = client.post('/api/transfers/',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'transfer_id' in data['data']
        assert 'reference_number' in data['data']
        assert data['data']['status'] == 'completed'
    
    def test_create_transfer_creates_transactions(self, client, auth_headers, test_accounts):
        """测试转账创建相应的交易记录"""
        bank_account, ewallet_account = test_accounts
        
        # 获取转账前的交易数量
        response = client.get('/api/transactions/', headers=auth_headers)
        initial_count = len(json.loads(response.data)['data']['transactions'])
        
        # 创建转账
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'currency': 'MYR',
            'description': '测试转账'
        }
        
        client.post('/api/transfers/',
                   data=json.dumps(transfer_data),
                   content_type='application/json',
                   headers=auth_headers)
        
        # 检查交易记录是否增加
        response = client.get('/api/transactions/', headers=auth_headers)
        final_count = len(json.loads(response.data)['data']['transactions'])
        
        # 应该增加2笔交易（转出和转入）
        assert final_count == initial_count + 2


class TestTransferRecords:
    """转账记录测试"""
    
    def test_get_transfers(self, client, auth_headers, test_accounts):
        """测试获取转账记录"""
        # 先创建一笔转账
        bank_account, ewallet_account = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'currency': 'MYR',
            'description': '测试转账'
        }
        
        client.post('/api/transfers/',
                   data=json.dumps(transfer_data),
                   content_type='application/json',
                   headers=auth_headers)
        
        # 获取转账记录
        response = client.get('/api/transfers/', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['transfers']) == 1
        
        transfer = data['data']['transfers'][0]
        assert transfer['amount'] == 100.0
        assert transfer['description'] == '测试转账'
        assert transfer['status'] == 'completed'
    
    def test_get_transfer_detail(self, client, auth_headers, test_accounts):
        """测试获取转账详情"""
        bank_account, ewallet_account = test_accounts
        
        # 创建转账
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0,
            'currency': 'MYR',
            'description': '测试转账'
        }
        
        create_response = client.post('/api/transfers/',
                                     data=json.dumps(transfer_data),
                                     content_type='application/json',
                                     headers=auth_headers)
        
        transfer_id = json.loads(create_response.data)['data']['transfer_id']
        
        # 获取转账详情
        response = client.get(f'/api/transfers/{transfer_id}', headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['id'] == transfer_id
        assert data['data']['amount'] == 100.0


class TestTransferLimits:
    """转账限额测试"""
    
    def test_transfer_limit_validation(self, client, auth_headers, test_accounts):
        """测试转账限额验证"""
        bank_account, ewallet_account = test_accounts
        
        # 尝试超过单笔限额的转账
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100000.0  # 超过单笔限额
        }
        
        response = client.post('/api/transfers/validate',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['success'] is False
        assert '限额' in data['error']


class TestTransferFees:
    """转账手续费测试"""
    
    def test_transfer_fee_calculation(self, client, auth_headers, test_accounts):
        """测试转账手续费计算"""
        bank_account, ewallet_account = test_accounts
        
        transfer_data = {
            'from_account_id': bank_account['id'],
            'to_account_id': ewallet_account['id'],
            'amount': 100.0
        }
        
        response = client.post('/api/transfers/validate',
                              data=json.dumps(transfer_data),
                              content_type='application/json',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        
        # 银行到电子钱包应该有固定手续费
        assert data['data']['fee'] == 1.0  # 根据配置
        assert data['data']['total_amount'] == 101.0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
