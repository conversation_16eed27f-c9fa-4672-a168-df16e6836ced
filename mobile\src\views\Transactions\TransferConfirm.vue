<template>
  <div class="transfer-confirm-page">
    <van-nav-bar
      title="确认转账"
      left-text="返回"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    />

    <!-- 进度指示器 -->
    <div class="progress-section">
      <van-steps :active="1" direction="horizontal">
        <van-step>填写信息</van-step>
        <van-step>确认转账</van-step>
        <van-step>转账完成</van-step>
      </van-steps>
    </div>

    <div class="confirm-content">
      <!-- 转账信息确认 -->
      <van-cell-group inset title="转账信息">
        <van-cell title="转出账户" :value="fromAccountName" :label="fromAccountInfo" />
        <van-cell title="转入账户" :value="toAccountName" :label="toAccountInfo" />
        <van-cell title="转账金额" :value="formatCurrency(transferData.amount, transferData.currency)" />
        <van-cell title="转账备注" :value="transferData.description || '无'" />
      </van-cell-group>

      <!-- 费用明细 -->
      <van-cell-group inset title="费用明细">
        <van-cell title="转账金额" :value="formatCurrency(transferData.amount, transferData.currency)" />
        <van-cell 
          title="手续费" 
          :value="formatCurrency(transferData.fee, transferData.currency)"
          :label="feeDescription"
        />
        <van-cell 
          title="总扣除金额" 
          :value="formatCurrency(transferData.totalAmount, transferData.currency)"
          class="total-amount"
        />
        <van-cell 
          title="实际到账金额" 
          :value="formatCurrency(transferData.amount, transferData.currency)"
        />
      </van-cell-group>

      <!-- 账户余额信息 -->
      <van-cell-group inset title="余额信息">
        <van-cell 
          title="转账前余额" 
          :value="formatCurrency(transferData.currentBalance, transferData.currency)"
        />
        <van-cell 
          title="转账后余额" 
          :value="formatCurrency(transferData.remainingBalance, transferData.currency)"
          :class="{ 'low-balance': transferData.remainingBalance < 100 }"
        />
      </van-cell-group>

      <!-- 安全验证 -->
      <van-cell-group inset title="安全验证">
        <van-field
          v-if="securityConfig.requirePassword"
          v-model="securityForm.password"
          type="password"
          label="支付密码"
          placeholder="请输入支付密码"
          :rules="[{ required: true, message: '请输入支付密码' }]"
          maxlength="6"
        />
        
        <van-cell v-if="securityConfig.supportBiometric && biometricAvailable">
          <template #title>
            <div class="biometric-option">
              <van-icon name="shield-o" />
              <span>指纹验证</span>
            </div>
          </template>
          <template #right-icon>
            <van-switch 
              v-model="securityForm.useBiometric" 
              @change="onBiometricToggle"
            />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 风险提示 -->
      <van-cell-group inset title="风险提示">
        <van-cell>
          <template #title>
            <div class="risk-warning">
              <van-icon name="warning-o" color="#ff6b35" />
              <div class="warning-content">
                <p>• 请仔细核对转账信息，转账完成后无法撤销</p>
                <p>• 请确保收款账户信息正确</p>
                <p>• 如有疑问，请联系客服</p>
              </div>
            </div>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 确认按钮 -->
      <div class="confirm-section">
        <van-button
          type="primary"
          block
          size="large"
          :loading="loading"
          @click="handleConfirm"
          :disabled="!isFormValid"
        >
          <van-icon name="passed" />
          确认转账
        </van-button>
        
        <van-button
          block
          size="large"
          @click="$router.back()"
          class="cancel-btn"
        >
          取消
        </van-button>
      </div>
    </div>

    <!-- 密码验证弹窗 -->
    <van-dialog
      v-model:show="showPasswordDialog"
      title="输入支付密码"
      show-cancel-button
      @confirm="verifyPassword"
      @cancel="showPasswordDialog = false"
    >
      <van-password-input
        v-model="passwordInput"
        :length="6"
        :gutter="10"
        @focus="passwordInputFocused = true"
      />
      <van-number-keyboard
        v-model="passwordInput"
        :show="passwordInputFocused"
        @blur="passwordInputFocused = false"
        @delete="passwordInput = passwordInput.slice(0, -1)"
      />
    </van-dialog>

    <!-- 生物识别验证弹窗 -->
    <van-dialog
      v-model:show="showBiometricDialog"
      title="生物识别验证"
      :show-cancel-button="false"
    >
      <div class="biometric-verify">
        <van-icon name="shield-o" size="48" color="#1989fa" />
        <p>请使用指纹或面容ID验证身份</p>
        <van-button type="primary" @click="performBiometricAuth">
          开始验证
        </van-button>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { transfersAPI } from '@/api/transfers'
import { showToast, showSuccessToast } from 'vant'
import { formatCurrency, formatAccountType } from '@/utils/format'

const router = useRouter()
const route = useRoute()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showPasswordDialog = ref(false)
const showBiometricDialog = ref(false)
const passwordInput = ref('')
const passwordInputFocused = ref(false)
const biometricAvailable = ref(false)

// 转账数据（从路由参数或状态获取）
const transferData = reactive({
  fromAccountId: '',
  toAccountId: '',
  amount: 0,
  currency: 'MYR',
  description: '',
  fee: 0,
  totalAmount: 0,
  currentBalance: 0,
  remainingBalance: 0,
  transferType: ''
})

// 安全验证配置
const securityConfig = reactive({
  requirePassword: true,
  supportBiometric: true,
  minPasswordLength: 6
})

// 安全验证表单
const securityForm = reactive({
  password: '',
  useBiometric: false,
  verified: false
})

// 计算属性
const fromAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === transferData.fromAccountId)
})

const toAccount = computed(() => {
  return accountsStore.accounts.find(account => account.id === transferData.toAccountId)
})

const fromAccountName = computed(() => {
  return fromAccount.value?.name || ''
})

const toAccountName = computed(() => {
  return toAccount.value?.name || ''
})

const fromAccountInfo = computed(() => {
  if (!fromAccount.value) return ''
  return `${formatAccountType(fromAccount.value.type)} • ${fromAccount.value.currency}`
})

const toAccountInfo = computed(() => {
  if (!toAccount.value) return ''
  return `${formatAccountType(toAccount.value.type)} • ${toAccount.value.currency}`
})

const feeDescription = computed(() => {
  if (transferData.fee === 0) return '免费'
  
  // 根据转账类型显示费用说明
  const feeDescriptions = {
    'bank_to_ewallet': '银行转电子钱包手续费',
    'bank_to_bank': '银行间转账手续费',
    'ewallet_to_ewallet': '电子钱包间转账手续费',
    'ewallet_to_bank': '电子钱包转银行手续费'
  }
  
  return feeDescriptions[transferData.transferType] || '转账手续费'
})

const isFormValid = computed(() => {
  if (securityConfig.requirePassword && !securityForm.verified) {
    return securityForm.password.length >= securityConfig.minPasswordLength || securityForm.useBiometric
  }
  return true
})

// 方法
const onBiometricToggle = (value) => {
  if (value && biometricAvailable.value) {
    showBiometricDialog.value = true
  }
}

const performBiometricAuth = async () => {
  try {
    // 这里应该调用实际的生物识别API
    // 模拟生物识别验证
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    securityForm.verified = true
    showBiometricDialog.value = false
    showSuccessToast('生物识别验证成功')
  } catch (error) {
    showToast('生物识别验证失败')
    securityForm.useBiometric = false
  }
}

const verifyPassword = async () => {
  if (passwordInput.value.length !== 6) {
    showToast('请输入6位支付密码')
    return
  }
  
  try {
    // 这里应该调用密码验证API
    // 模拟密码验证
    if (passwordInput.value === '123456') {
      securityForm.verified = true
      showPasswordDialog.value = false
      showSuccessToast('密码验证成功')
    } else {
      showToast('支付密码错误')
    }
  } catch (error) {
    showToast('密码验证失败')
  }
  
  passwordInput.value = ''
}

const handleConfirm = async () => {
  // 如果需要密码验证且未验证
  if (securityConfig.requirePassword && !securityForm.verified) {
    if (securityForm.useBiometric) {
      showBiometricDialog.value = true
      return
    } else {
      showPasswordDialog.value = true
      return
    }
  }

  loading.value = true

  try {
    const response = await transfersAPI.createTransfer({
      from_account_id: transferData.fromAccountId,
      to_account_id: transferData.toAccountId,
      amount: transferData.amount,
      currency: transferData.currency,
      description: transferData.description
    })

    if (response.success) {
      // 刷新账户数据
      await accountsStore.fetchAccounts()

      // 准备成功页面数据
      const successData = {
        amount: transferData.amount,
        currency: transferData.currency,
        fee: transferData.fee,
        fromAccountName: fromAccountName.value,
        toAccountName: toAccountName.value,
        referenceNumber: response.data.reference_number,
        completedAt: response.data.completed_at,
        transferId: response.data.transfer_id
      }

      // 跳转到转账成功页面
      router.replace({
        name: 'TransferSuccess',
        query: {
          data: encodeURIComponent(JSON.stringify(successData))
        }
      })
    } else {
      showToast(response.error || '转账失败')
    }
  } catch (error) {
    console.error('转账失败:', error)
    showToast('转账失败，请重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 从路由参数获取转账数据
  if (route.query.data) {
    try {
      const data = JSON.parse(decodeURIComponent(route.query.data))
      Object.assign(transferData, data)
    } catch (error) {
      console.error('解析转账数据失败:', error)
      showToast('转账数据错误')
      router.back()
      return
    }
  } else {
    showToast('缺少转账数据')
    router.back()
    return
  }
  
  // 加载账户数据
  await accountsStore.fetchAccounts()
  
  // 检查生物识别可用性
  // 这里应该检查设备是否支持生物识别
  biometricAvailable.value = true // 模拟支持
})
</script>

<style scoped>
.transfer-confirm-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.confirm-content {
  padding: 16px;
}

.total-amount .van-cell__value {
  color: #ee0a24;
  font-weight: bold;
  font-size: 16px;
}

.low-balance .van-cell__value {
  color: #ff6b35;
}

.biometric-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-warning {
  display: flex;
  gap: 12px;
}

.warning-content p {
  margin: 4px 0;
  font-size: 14px;
  color: #646566;
  line-height: 1.4;
}

.confirm-section {
  margin-top: 32px;
  padding: 0 16px;
}

.confirm-section .van-button {
  height: 50px;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
}

.cancel-btn {
  color: #646566;
  border-color: #ebedf0;
}

.biometric-verify {
  text-align: center;
  padding: 24px;
}

.biometric-verify p {
  margin: 16px 0;
  color: #646566;
}

.progress-section {
  background: white;
  padding: 16px;
  margin-bottom: 8px;
}
</style>
