-- 转账功能数据库迁移脚本
-- 创建日期: 2024-12-08
-- 描述: 添加转账功能相关的数据表

-- 1. 创建转账记录表
CREATE TABLE IF NOT EXISTS transfers (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    from_account_id TEXT NOT NULL,
    to_account_id TEXT NOT NULL,
    amount REAL NOT NULL,
    fee REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    transfer_type TEXT NOT NULL,
    reference_number TEXT,
    created_at TEXT NOT NULL,
    completed_at TEXT,
    failed_at TEXT,
    failure_reason TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (from_account_id) REFERENCES accounts (id),
    FOREIGN KEY (to_account_id) REFERENCES accounts (id)
);

-- 2. 创建转账配置表
CREATE TABLE IF NOT EXISTS transfer_config (
    id TEXT PRIMARY KEY,
    transfer_type TEXT NOT NULL UNIQUE,
    fee_type TEXT NOT NULL,
    fee_amount REAL,
    fee_rate REAL,
    fee_min REAL,
    fee_max REAL,
    limit_min REAL NOT NULL,
    limit_max REAL NOT NULL,
    daily_limit REAL,
    monthly_limit REAL,
    is_active BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 3. 创建转账模板表
CREATE TABLE IF NOT EXISTS transfer_templates (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    template_name TEXT NOT NULL,
    from_account_id TEXT NOT NULL,
    to_account_id TEXT NOT NULL,
    amount REAL,
    description TEXT,
    is_favorite BOOLEAN DEFAULT 0,
    usage_count INTEGER DEFAULT 0,
    last_used_at TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (from_account_id) REFERENCES accounts (id),
    FOREIGN KEY (to_account_id) REFERENCES accounts (id)
);

-- 4. 创建转账限额使用记录表
CREATE TABLE IF NOT EXISTS transfer_limits_usage (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    transfer_type TEXT NOT NULL,
    date TEXT NOT NULL,
    daily_amount REAL DEFAULT 0,
    daily_count INTEGER DEFAULT 0,
    monthly_amount REAL DEFAULT 0,
    monthly_count INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    UNIQUE(user_id, transfer_type, date)
);

-- 5. 创建索引
-- 转账记录表索引
CREATE INDEX IF NOT EXISTS idx_transfers_user_id ON transfers(user_id);
CREATE INDEX IF NOT EXISTS idx_transfers_from_account ON transfers(from_account_id);
CREATE INDEX IF NOT EXISTS idx_transfers_to_account ON transfers(to_account_id);
CREATE INDEX IF NOT EXISTS idx_transfers_status ON transfers(status);
CREATE INDEX IF NOT EXISTS idx_transfers_created_at ON transfers(created_at);
CREATE INDEX IF NOT EXISTS idx_transfers_user_date ON transfers(user_id, created_at);

-- 转账模板表索引
CREATE INDEX IF NOT EXISTS idx_transfer_templates_user_id ON transfer_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_transfer_templates_favorite ON transfer_templates(user_id, is_favorite);
CREATE INDEX IF NOT EXISTS idx_transfer_templates_usage ON transfer_templates(user_id, usage_count DESC);

-- 转账限额使用记录表索引
CREATE INDEX IF NOT EXISTS idx_transfer_limits_user_date ON transfer_limits_usage(user_id, date);
CREATE INDEX IF NOT EXISTS idx_transfer_limits_user_type_date ON transfer_limits_usage(user_id, transfer_type, date);

-- 6. 插入默认转账配置数据
INSERT OR REPLACE INTO transfer_config (
    id, transfer_type, fee_type, fee_amount, fee_rate, fee_min, fee_max,
    limit_min, limit_max, daily_limit, monthly_limit, is_active, created_at, updated_at
) VALUES 
-- 银行账户到电子钱包
('config_bank_to_ewallet', 'bank_to_ewallet', 'fixed', 1.0, NULL, NULL, NULL,
 1.0, 10000.0, 5000.0, 50000.0, 1, datetime('now'), datetime('now')),

-- 银行账户到银行账户
('config_bank_to_bank', 'bank_to_bank', 'percentage', NULL, 0.001, 1.0, 10.0,
 1.0, 50000.0, 20000.0, 200000.0, 1, datetime('now'), datetime('now')),

-- 电子钱包到电子钱包
('config_ewallet_to_ewallet', 'ewallet_to_ewallet', 'fixed', 0.5, NULL, NULL, NULL,
 1.0, 5000.0, 3000.0, 30000.0, 1, datetime('now'), datetime('now')),

-- 电子钱包到银行账户
('config_ewallet_to_bank', 'ewallet_to_bank', 'fixed', 2.0, NULL, NULL, NULL,
 1.0, 10000.0, 8000.0, 80000.0, 1, datetime('now'), datetime('now'));

-- 7. 创建转账状态检查约束（SQLite不直接支持CHECK约束，在应用层验证）
-- 有效状态: 'pending', 'completed', 'failed', 'cancelled'
-- 有效转账类型: 'bank_to_ewallet', 'bank_to_bank', 'ewallet_to_ewallet', 'ewallet_to_bank'
-- 有效手续费类型: 'fixed', 'percentage'

-- 8. 创建触发器确保数据一致性
-- 确保转出账户和转入账户不能相同
CREATE TRIGGER IF NOT EXISTS check_transfer_accounts_different
BEFORE INSERT ON transfers
WHEN NEW.from_account_id = NEW.to_account_id
BEGIN
    SELECT RAISE(ABORT, '转出账户和转入账户不能相同');
END;

-- 确保转账金额大于0
CREATE TRIGGER IF NOT EXISTS check_transfer_amount_positive
BEFORE INSERT ON transfers
WHEN NEW.amount <= 0
BEGIN
    SELECT RAISE(ABORT, '转账金额必须大于0');
END;

-- 确保手续费不为负数
CREATE TRIGGER IF NOT EXISTS check_transfer_fee_non_negative
BEFORE INSERT ON transfers
WHEN NEW.fee < 0
BEGIN
    SELECT RAISE(ABORT, '手续费不能为负数');
END;

-- 9. 创建视图简化查询
-- 转账记录详情视图
CREATE VIEW IF NOT EXISTS transfer_details AS
SELECT 
    t.id,
    t.user_id,
    t.amount,
    t.fee,
    t.currency,
    t.description,
    t.status,
    t.transfer_type,
    t.reference_number,
    t.created_at,
    t.completed_at,
    fa.name as from_account_name,
    fa.type as from_account_type,
    ta.name as to_account_name,
    ta.type as to_account_type
FROM transfers t
LEFT JOIN accounts fa ON t.from_account_id = fa.id
LEFT JOIN accounts ta ON t.to_account_id = ta.id;

-- 用户转账统计视图
CREATE VIEW IF NOT EXISTS user_transfer_stats AS
SELECT 
    user_id,
    COUNT(*) as total_transfers,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_transfers,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
    SUM(CASE WHEN status = 'completed' THEN fee ELSE 0 END) as total_fees,
    MAX(created_at) as last_transfer_date
FROM transfers
GROUP BY user_id;
